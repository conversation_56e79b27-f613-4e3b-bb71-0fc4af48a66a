import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { useAuthStore } from "./auth";
import { useBusinessStore } from "./business";
import { createDebugger } from "src/utils/debug";
import {
  MenuService,
  MenuCategoriesService,
  MenuItemsService,
} from "src/services/MenuService";
import {
  isIngredientId,
  isSizeId,
  isUnitId,
  isAddonId,
  generateIngredientId,
  generateSizeId,
  generateAddonId,
} from "src/utils/idGenerator";

export const useMenuStore = defineStore("menu", () => {
  // Create a debugger for this store
  const debug = createDebugger("store:menu");
  const authStore = useAuthStore();
  const businessStore = useBusinessStore();

  // Create service instances
  const menuService = new MenuService();
  const menuCategoriesService = new MenuCategoriesService();
  const menuItemsService = new MenuItemsService();

  // State
  const menus = ref([]);
  const currentMenu = ref(null);
  const categories = ref([]);
  const items = ref([]);
  const loading = ref(false);
  const error = ref(null);

  // Getters
  const hasMenus = computed(() => menus.value.length > 0);
  const activeMenus = computed(() =>
    menus.value.filter((menu) => menu.isActive && !menu.deleted)
  );
  const defaultMenu = computed(() =>
    menus.value.find((menu) => menu.isDefault && menu.isActive && !menu.deleted)
  );

  /**
   * Fetch all menus for a business
   * @param {string} businessId - The business ID
   * @param {boolean} includeDeleted - Whether to include deleted menus
   * @returns {Promise<Array>} The menus
   */
  async function fetchBusinessMenus(businessId, includeDeleted = false) {
    if (!authStore.isAuthenticated) {
      throw new Error("User must be authenticated to fetch menus");
    }

    debug(`Fetching menus for business: ${businessId}`);
    loading.value = true;
    error.value = null;

    try {
      const { data } = await menuService.getBusinessMenus(
        businessId,
        includeDeleted
      );
      menus.value = data;
      return data;
    } catch (err) {
      debug("Error fetching business menus: %O", err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Get a menu by ID or slug (internal method, prefer getCompleteMenu for most use cases)
   * @param {string} menuIdOrSlug - The menu ID or slug
   * @param {string} businessId - The business ID (required if using slug)
   * @returns {Promise<Object>} The menu
   * @private
   */
  async function _getMenu(menuIdOrSlug, businessId = null) {
    debug(`Getting menu metadata: ${menuIdOrSlug}`);

    try {
      // If businessId is not provided but we have a current business, use its ID
      if (!businessId && businessStore.currentBusiness) {
        businessId = businessStore.currentBusiness.id;
      }

      const { data } = await menuService.getMenu(menuIdOrSlug, businessId);
      return data;
    } catch (err) {
      debug("Error getting menu metadata: %O", err);
      throw err;
    }
  }

  /**
   * Get a complete menu with categories and items
   * @param {string} menuIdOrSlug - The menu ID or slug
   * @param {string} businessId - The business ID (required if using slug)
   * @param {boolean} setAsCurrent - Whether to set the menu as the current menu
   * @returns {Promise<Object>} The complete menu with categories and items
   */
  async function getMenu(
    menuIdOrSlug,
    businessId = null,
    setAsCurrent = false
  ) {
    debug(`Getting complete menu: ${menuIdOrSlug}`);
    loading.value = true;
    error.value = null;

    try {
      // If businessId is not provided but we have a current business, use its ID
      if (!businessId && businessStore.currentBusiness) {
        businessId = businessStore.currentBusiness.id;
      }

      const { data } = await menuService.getCompleteMenu(
        menuIdOrSlug,
        businessId
      );

      if (setAsCurrent) {
        currentMenu.value = data;
        categories.value = data.categories;
        items.value = data.categories.flatMap((category) => category.items);
      }

      return data;
    } catch (err) {
      debug("Error getting complete menu: %O", err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Create a new menu
   * @param {Object} menuData - The menu data
   * @returns {Promise<Object>} The created menu
   */
  async function createMenu(menuData) {
    if (!authStore.isAuthenticated) {
      throw new Error("User must be authenticated to create a menu");
    }

    debug(`Creating menu for business: ${menuData.businessId}`);
    loading.value = true;
    error.value = null;

    try {
      const { data } = await menuService.createMenu(menuData);
      menus.value.push(data[0]);
      return data[0];
    } catch (err) {
      debug("Error creating menu: %O", err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Update a menu
   * @param {string} menuId - The menu ID
   * @param {Object} updates - The updates to apply
   * @returns {Promise<Object>} The updated menu
   */
  async function updateMenu(menuId, updates) {
    if (!authStore.isAuthenticated) {
      throw new Error("User must be authenticated to update a menu");
    }

    debug(`Updating menu: ${menuId}`);
    loading.value = true;
    error.value = null;

    try {
      const { data } = await menuService.updateMenu(menuId, updates);

      // Update the menu in the menus array
      const index = menus.value.findIndex((menu) => menu.id === menuId);
      if (index !== -1) {
        menus.value[index] = { ...menus.value[index], ...data[0] };
      }

      // Update current menu if it's the one being updated
      if (currentMenu.value && currentMenu.value.id === menuId) {
        currentMenu.value = { ...currentMenu.value, ...data[0] };
      }

      return data[0];
    } catch (err) {
      debug("Error updating menu: %O", err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Soft delete a menu
   * @param {string} menuId - The menu ID
   * @returns {Promise<Object>} The result of the operation
   */
  async function deleteMenu(menuId) {
    if (!authStore.isAuthenticated) {
      throw new Error("User must be authenticated to delete a menu");
    }

    debug(`Soft deleting menu: ${menuId}`);
    loading.value = true;
    error.value = null;

    try {
      const { data } = await menuService.deleteMenu(menuId);

      // Update the menu in the menus array
      const index = menus.value.findIndex((menu) => menu.id === menuId);
      if (index !== -1) {
        menus.value[index] = { ...menus.value[index], deleted: true };
      }

      // Clear current menu if it's the one being deleted
      if (currentMenu.value && currentMenu.value.id === menuId) {
        currentMenu.value = null;
      }

      return data[0];
    } catch (err) {
      debug("Error deleting menu: %O", err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Create a new category
   * @param {Object} categoryData - The category data
   * @returns {Promise<Object>} The created category
   */
  async function createCategory(categoryData) {
    if (!authStore.isAuthenticated) {
      throw new Error("User must be authenticated to create a category");
    }

    debug(`Creating category for menu: ${categoryData.menuId}`);
    loading.value = true;
    error.value = null;

    try {
      // Ensure categoryIngredients is an array of IDs
      if (
        categoryData.categoryIngredients &&
        Array.isArray(categoryData.categoryIngredients)
      ) {
        // If we have objects with IDs, extract just the IDs
        if (
          categoryData.categoryIngredients.length > 0 &&
          typeof categoryData.categoryIngredients[0] === "object"
        ) {
          categoryData.categoryIngredients =
            categoryData.categoryIngredients.map((ing) => ing.id);
        }
      } else {
        categoryData.categoryIngredients = [];
      }

      // Ensure each addon has an ID
      if (
        categoryData.categoryAddons &&
        Array.isArray(categoryData.categoryAddons)
      ) {
        categoryData.categoryAddons = categoryData.categoryAddons.map(
          (addon) => {
            if (!addon.id) {
              addon.id = generateAddonId();
            }
            return addon;
          }
        );
      } else {
        categoryData.categoryAddons = [];
      }

      const { data } = await menuCategoriesService.createCategory(categoryData);
      categories.value.push(data[0]);

      // Update current menu categories if applicable
      if (currentMenu.value && currentMenu.value.id === categoryData.menuId) {
        if (!currentMenu.value.categories) {
          currentMenu.value.categories = [];
        }
        currentMenu.value.categories.push(data[0]);
      }

      return data[0];
    } catch (err) {
      debug("Error creating category: %O", err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Update a category
   * @param {string} categoryId - The category ID
   * @param {Object} updates - The updates to apply
   * @returns {Promise<Object>} The updated category
   */
  async function updateCategory(categoryId, updates) {
    if (!authStore.isAuthenticated) {
      throw new Error("User must be authenticated to update a category");
    }

    debug(`Updating category: ${categoryId}`);
    loading.value = true;
    error.value = null;

    try {
      // Ensure categoryIngredients is an array of IDs
      if (
        updates.categoryIngredients &&
        Array.isArray(updates.categoryIngredients)
      ) {
        // If we have objects with IDs, extract just the IDs
        if (
          updates.categoryIngredients.length > 0 &&
          typeof updates.categoryIngredients[0] === "object"
        ) {
          updates.categoryIngredients = updates.categoryIngredients.map(
            (ing) => ing.id
          );
        }
      }

      // Ensure each addon has an ID
      if (updates.categoryAddons && Array.isArray(updates.categoryAddons)) {
        updates.categoryAddons = updates.categoryAddons.map((addon) => {
          if (!addon.id) {
            addon.id = generateAddonId();
          }
          return addon;
        });
      }

      const { data } = await menuCategoriesService.updateCategory(
        categoryId,
        updates
      );

      // Update the category in the categories array
      const index = categories.value.findIndex(
        (category) => category.id === categoryId
      );
      if (index !== -1) {
        categories.value[index] = { ...categories.value[index], ...data[0] };
      }

      // Update current menu categories if applicable
      if (currentMenu.value && currentMenu.value.categories) {
        const menuCategoryIndex = currentMenu.value.categories.findIndex(
          (category) => category.id === categoryId
        );
        if (menuCategoryIndex !== -1) {
          currentMenu.value.categories[menuCategoryIndex] = {
            ...currentMenu.value.categories[menuCategoryIndex],
            ...data[0],
          };
        }
      }

      return data[0];
    } catch (err) {
      debug("Error updating category: %O", err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Check if a category slug is available within a menu
   * @param {string} menuId - The menu ID
   * @param {string} slug - The slug to check
   * @param {string} [excludeCategoryId] - Category ID to exclude from check (for editing)
   * @returns {Promise<Object>} Object with available flag
   */
  async function checkCategorySlugAvailability(
    menuId,
    slug,
    excludeCategoryId = null
  ) {
    debug(`Checking category slug availability: ${slug} in menu ${menuId}`);
    try {
      const { available: isAvailable } =
        await menuCategoriesService.checkCategorySlugAvailability(
          menuId,
          slug,
          excludeCategoryId
        );
      debug(
        `Category slug '${slug}' is ${
          isAvailable ? "available" : "taken"
        } in menu ${menuId}`
      );
      return { available: isAvailable };
    } catch (err) {
      debug("Error checking category slug availability: %O", err);
      error.value = err.message;
      throw err;
    }
  }

  /**
   * Soft delete a category
   * @param {string} categoryId - The category ID
   * @returns {Promise<Object>} The result of the operation
   */
  async function deleteCategory(categoryId) {
    if (!authStore.isAuthenticated) {
      throw new Error("User must be authenticated to delete a category");
    }

    debug(`Soft deleting category: ${categoryId}`);
    loading.value = true;
    error.value = null;

    try {
      const { data } = await menuCategoriesService.deleteCategory(categoryId);

      // Update the category in the categories array
      const index = categories.value.findIndex(
        (category) => category.id === categoryId
      );
      if (index !== -1) {
        categories.value[index] = {
          ...categories.value[index],
          deleted: true,
          slug: data[0].slug, // Update with the new timestamped slug
        };
      }

      // Update current menu categories if applicable
      if (currentMenu.value && currentMenu.value.categories) {
        const menuCategoryIndex = currentMenu.value.categories.findIndex(
          (category) => category.id === categoryId
        );
        if (menuCategoryIndex !== -1) {
          currentMenu.value.categories[menuCategoryIndex] = {
            ...currentMenu.value.categories[menuCategoryIndex],
            deleted: true,
            slug: data[0].slug, // Update with the new timestamped slug
          };
        }
      }

      return data[0];
    } catch (err) {
      debug("Error deleting category: %O", err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Create a new menu item
   * @param {Object} itemData - The item data
   * @returns {Promise<Object>} The created item
   */
  async function createMenuItem(itemData) {
    if (!authStore.isAuthenticated) {
      throw new Error("User must be authenticated to create a menu item");
    }

    debug(`Creating item for menu: ${itemData.menuId}`);
    loading.value = true;
    error.value = null;

    try {
      // Ensure ingredients is in the correct format (added/removed structure)
      if (!itemData.ingredients || typeof itemData.ingredients !== "object") {
        itemData.ingredients = { added: [], removed: [] };
      } else {
        // If ingredients is an array of objects, convert to the diff structure
        if (Array.isArray(itemData.ingredients)) {
          // Find the category to get its ingredients
          const category = currentMenu.value?.categories?.find(
            (cat) => cat.id === itemData.categoryId
          );

          if (category && category.categoryIngredients) {
            // Extract just the IDs if we have objects
            const selectedIngredientIds = itemData.ingredients.map((ing) =>
              typeof ing === "object" ? ing.id : ing
            );

            // Generate the diff structure
            itemData.ingredients = generateIngredientModifications(
              selectedIngredientIds,
              category.categoryIngredients
            );
          } else {
            // If we can't find the category, just add all as added ingredients
            itemData.ingredients = {
              added: itemData.ingredients.map((ing) =>
                typeof ing === "object" ? ing.id : ing
              ),
              removed: [],
            };
          }
        } else {
          // Ensure added and removed are arrays
          if (!Array.isArray(itemData.ingredients.added)) {
            itemData.ingredients.added = [];
          }
          if (!Array.isArray(itemData.ingredients.removed)) {
            itemData.ingredients.removed = [];
          }
        }
      }

      // Ensure sizes use sizeId format
      if (itemData.sizes && Array.isArray(itemData.sizes)) {
        itemData.sizes = itemData.sizes.map((size) => {
          // If it's already in the correct format, return as is
          if (size.sizeId) return size;

          // If it's a full size object, extract the ID and price adjustment
          if (size.id) {
            return {
              sizeId: size.id,
              priceAdjustment:
                size.defaultAdjustment || size.priceAdjustment || 0,
            };
          }

          // If it's just an ID string
          if (typeof size === "string") {
            return {
              sizeId: size,
              priceAdjustment: 0,
            };
          }

          return size;
        });
      }

      // Ensure variations have IDs
      if (itemData.variations && Array.isArray(itemData.variations)) {
        itemData.variations = itemData.variations.map((variation) => {
          if (!variation.id) {
            variation.id = `var-${generateIngredientId().substring(2)}`;
          }
          return variation;
        });
      }

      // Ensure addons structure is correct
      if (itemData.addons && typeof itemData.addons === "object") {
        // If addons is an array, convert to the diff structure
        if (Array.isArray(itemData.addons)) {
          itemData.addons = {
            useCategory: true,
            added: itemData.addons.map((addon) => {
              if (!addon.id) {
                addon.id = generateAddonId();
              }
              return addon;
            }),
            removed: [],
          };
        } else {
          // Ensure added and removed are arrays
          if (!Array.isArray(itemData.addons.added)) {
            itemData.addons.added = [];
          }
          if (!Array.isArray(itemData.addons.removed)) {
            itemData.addons.removed = [];
          }

          // Ensure each added addon has an ID
          itemData.addons.added = itemData.addons.added.map((addon) => {
            if (!addon.id) {
              addon.id = generateAddonId();
            }
            return addon;
          });
        }
      }

      const { data } = await menuItemsService.createItem(itemData);
      items.value.push(data[0]);

      // Update current menu items if applicable
      if (
        currentMenu.value &&
        currentMenu.value.categories &&
        itemData.categoryId
      ) {
        const categoryIndex = currentMenu.value.categories.findIndex(
          (category) => category.id === itemData.categoryId
        );
        if (categoryIndex !== -1) {
          if (!currentMenu.value.categories[categoryIndex].items) {
            currentMenu.value.categories[categoryIndex].items = [];
          }
          currentMenu.value.categories[categoryIndex].items.push(data[0]);
        }
      }

      return data[0];
    } catch (err) {
      debug("Error creating menu item: %O", err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Update a menu item
   * @param {string} itemId - The item ID
   * @param {Object} updates - The updates to apply
   * @returns {Promise<Object>} The updated item
   */
  async function updateMenuItem(itemId, updates) {
    if (!authStore.isAuthenticated) {
      throw new Error("User must be authenticated to update a menu item");
    }

    debug(`Updating item: ${itemId}`);
    loading.value = true;
    error.value = null;

    try {
      // Find the item's category
      let category = null;

      if (currentMenu.value && currentMenu.value.categories) {
        for (const cat of currentMenu.value.categories) {
          if (cat.items) {
            const item = cat.items.find((item) => item.id === itemId);
            if (item) {
              category = cat;
              break;
            }
          }
        }
      }

      // Ensure ingredients is in the correct format (added/removed structure)
      if (updates.ingredients) {
        // If ingredients is an array of objects, convert to the diff structure
        if (Array.isArray(updates.ingredients)) {
          if (category && category.categoryIngredients) {
            // Extract just the IDs if we have objects
            const selectedIngredientIds = updates.ingredients.map((ing) =>
              typeof ing === "object" ? ing.id : ing
            );

            // Generate the diff structure
            updates.ingredients = generateIngredientModifications(
              selectedIngredientIds,
              category.categoryIngredients
            );
          } else {
            // If we can't find the category, just add all as added ingredients
            updates.ingredients = {
              added: updates.ingredients.map((ing) =>
                typeof ing === "object" ? ing.id : ing
              ),
              removed: [],
            };
          }
        } else if (typeof updates.ingredients === "object") {
          // Ensure added and removed are arrays
          if (!Array.isArray(updates.ingredients.added)) {
            updates.ingredients.added = [];
          }
          if (!Array.isArray(updates.ingredients.removed)) {
            updates.ingredients.removed = [];
          }
        }
      }

      // Ensure sizes use sizeId format
      if (updates.sizes && Array.isArray(updates.sizes)) {
        updates.sizes = updates.sizes.map((size) => {
          // If it's already in the correct format, return as is
          if (size.sizeId) return size;

          // If it's a full size object, extract the ID and price adjustment
          if (size.id) {
            return {
              sizeId: size.id,
              priceAdjustment:
                size.defaultAdjustment || size.priceAdjustment || 0,
            };
          }

          // If it's just an ID string
          if (typeof size === "string") {
            return {
              sizeId: size,
              priceAdjustment: 0,
            };
          }

          return size;
        });
      }

      // Ensure variations have IDs
      if (updates.variations && Array.isArray(updates.variations)) {
        updates.variations = updates.variations.map((variation) => {
          if (!variation.id) {
            variation.id = `var-${generateIngredientId().substring(2)}`;
          }
          return variation;
        });
      }

      // Ensure addons structure is correct
      if (updates.addons && typeof updates.addons === "object") {
        // If addons is an array, convert to the diff structure
        if (Array.isArray(updates.addons)) {
          updates.addons = {
            useCategory: true,
            added: updates.addons.map((addon) => {
              if (!addon.id) {
                addon.id = generateAddonId();
              }
              return addon;
            }),
            removed: [],
          };
        } else {
          // Ensure added and removed are arrays
          if (!Array.isArray(updates.addons.added)) {
            updates.addons.added = [];
          }
          if (!Array.isArray(updates.addons.removed)) {
            updates.addons.removed = [];
          }

          // Ensure each added addon has an ID
          updates.addons.added = updates.addons.added.map((addon) => {
            if (!addon.id) {
              addon.id = generateAddonId();
            }
            return addon;
          });
        }
      }

      const { data } = await menuItemsService.updateItem(itemId, updates);

      // Update the item in the items array
      const index = items.value.findIndex((item) => item.id === itemId);
      if (index !== -1) {
        items.value[index] = { ...items.value[index], ...data[0] };
      }

      // Update current menu items if applicable
      if (currentMenu.value && currentMenu.value.categories) {
        for (const category of currentMenu.value.categories) {
          if (category.items) {
            const itemIndex = category.items.findIndex(
              (item) => item.id === itemId
            );
            if (itemIndex !== -1) {
              category.items[itemIndex] = {
                ...category.items[itemIndex],
                ...data[0],
              };
              break;
            }
          }
        }
      }

      return data[0];
    } catch (err) {
      debug("Error updating menu item: %O", err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Soft delete a menu item
   * @param {string} itemId - The item ID
   * @returns {Promise<Object>} The result of the operation
   */
  async function deleteMenuItem(itemId) {
    if (!authStore.isAuthenticated) {
      throw new Error("User must be authenticated to delete a menu item");
    }

    debug(`Soft deleting item: ${itemId}`);
    loading.value = true;
    error.value = null;

    try {
      const { data } = await menuItemsService.deleteItem(itemId);

      // Update the item in the items array
      const index = items.value.findIndex((item) => item.id === itemId);
      if (index !== -1) {
        items.value[index] = { ...items.value[index], deleted: true };
      }

      // Update current menu items if applicable
      if (currentMenu.value && currentMenu.value.categories) {
        for (const category of currentMenu.value.categories) {
          if (category.items) {
            const itemIndex = category.items.findIndex(
              (item) => item.id === itemId
            );
            if (itemIndex !== -1) {
              category.items[itemIndex] = {
                ...category.items[itemIndex],
                deleted: true,
              };
              break;
            }
          }
        }
      }

      return data[0];
    } catch (err) {
      debug("Error deleting menu item: %O", err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Set the current menu
   * @param {Object} menu - The menu to set as current
   */
  function setCurrentMenu(menu) {
    currentMenu.value = menu;
  }

  /**
   * Clear the current menu
   */
  function clearCurrentMenu() {
    currentMenu.value = null;
    categories.value = [];
    items.value = [];
  }

  /**
   * Helper Methods for ID Resolution and Ingredient Modifications
   */

  /**
   * Resolve ingredient IDs to full ingredient objects
   * @param {Array<string>} ingredientIds - Array of ingredient IDs
   * @returns {Array<Object>} Array of ingredient objects
   */
  function resolveIngredientIds(ingredientIds) {
    if (!ingredientIds || !Array.isArray(ingredientIds)) return [];

    return ingredientIds.map((id) => {
      // Validate that this is actually an ingredient ID
      if (!isIngredientId(id)) {
        debug(`Invalid ingredient ID format: ${id}`);
        return { id, name: "Invalid ID", category: "error" };
      }

      // Find the ingredient in the business store
      if (
        businessStore.currentBusiness &&
        businessStore.currentBusiness.ingredients
      ) {
        const ingredient = businessStore.currentBusiness.ingredients.find(
          (ing) => ing.id === id
        );
        if (ingredient) return ingredient;
      }

      // If not found, return a placeholder
      return { id, name: "Unknown Ingredient", category: "unknown" };
    });
  }

  /**
   * Resolve size IDs to full size objects
   * @param {Array<string>} sizeIds - Array of size IDs
   * @returns {Array<Object>} Array of size objects
   */
  function resolveSizeIds(sizeIds) {
    if (!sizeIds || !Array.isArray(sizeIds)) return [];

    return sizeIds.map((id) => {
      // Validate that this is actually a size ID
      if (!isSizeId(id)) {
        debug(`Invalid size ID format: ${id}`);
        return {
          id,
          name: "Invalid Size",
          adjustmentType: "absolute",
          defaultAdjustment: 0,
        };
      }

      // Find the size in the business store
      if (
        businessStore.currentBusiness &&
        businessStore.currentBusiness.size_options
      ) {
        const size = businessStore.currentBusiness.size_options.find(
          (s) => s.id === id
        );
        if (size) return size;
      }

      // If not found, return a placeholder
      return {
        id,
        name: "Unknown Size",
        adjustmentType: "absolute",
        defaultAdjustment: 0,
      };
    });
  }

  /**
   * Resolve unit IDs to full unit objects
   * @param {Array<string>} unitIds - Array of unit IDs
   * @returns {Array<Object>} Array of unit objects
   */
  function resolveUnitIds(unitIds) {
    if (!unitIds || !Array.isArray(unitIds)) return [];

    return unitIds.map((id) => {
      // Validate that this is actually a unit ID
      if (!isUnitId(id)) {
        debug(`Invalid unit ID format: ${id}`);
        return { id, name: "Invalid Unit", abbreviation: "?" };
      }

      // Find the unit in the business store
      if (
        businessStore.currentBusiness &&
        businessStore.currentBusiness.unit_options
      ) {
        const unit = businessStore.currentBusiness.unit_options.find(
          (u) => u.id === id
        );
        if (unit) return unit;
      }

      // If not found, return a placeholder
      return { id, name: "Unknown Unit", abbreviation: "?" };
    });
  }

  /**
   * Resolve addon IDs to full addon objects
   * @param {Array<string>} addonIds - Array of addon IDs
   * @param {Array<Object>} categoryAddons - Category addons to search in
   * @returns {Array<Object>} Array of addon objects
   */
  function resolveAddonIds(addonIds, categoryAddons = []) {
    if (!addonIds || !Array.isArray(addonIds)) return [];

    return addonIds.map((id) => {
      // Validate that this is actually an addon ID
      if (!isAddonId(id)) {
        debug(`Invalid addon ID format: ${id}`);
        return { id, name: "Invalid Addon", price: 0 };
      }

      // Find the addon in the category addons
      const addon = categoryAddons.find((a) => a.id === id);
      if (addon) return addon;

      // If not found, return a placeholder
      return { id, name: "Unknown Addon", price: 0 };
    });
  }

  /**
   * Apply ingredient modifications to get effective ingredients
   * @param {Array<string>} categoryIngredientIds - Category ingredient IDs
   * @param {Object} modifications - Ingredient modifications with added and removed arrays
   * @returns {Array<string>} Effective ingredient IDs
   */
  function applyIngredientModifications(categoryIngredientIds, modifications) {
    if (!categoryIngredientIds) return [];
    if (!modifications) return [...categoryIngredientIds];

    // Start with category ingredients
    const effectiveIds = [...categoryIngredientIds];

    // Remove ingredients that are in the removed list
    const removed = modifications.removed || [];
    const filteredIds = effectiveIds.filter((id) => !removed.includes(id));

    // Add ingredients that are in the added list
    const added = modifications.added || [];
    added.forEach((id) => {
      if (!filteredIds.includes(id)) {
        filteredIds.push(id);
      }
    });

    return filteredIds;
  }

  /**
   * Generate ingredient modifications based on selected ingredients and category ingredients
   * @param {Array<string>} selectedIngredientIds - Selected ingredient IDs
   * @param {Array<string>} categoryIngredientIds - Category ingredient IDs
   * @returns {Object} Ingredient modifications with added and removed arrays
   */
  function generateIngredientModifications(
    selectedIngredientIds,
    categoryIngredientIds
  ) {
    if (!selectedIngredientIds) selectedIngredientIds = [];
    if (!categoryIngredientIds) categoryIngredientIds = [];

    // Initialize the modifications structure
    const modifications = {
      added: [],
      removed: [],
    };

    // Find ingredients that are in the category but not selected (removed)
    modifications.removed = categoryIngredientIds.filter(
      (id) => !selectedIngredientIds.includes(id)
    );

    // Find ingredients that are selected but not in the category (added)
    modifications.added = selectedIngredientIds.filter(
      (id) => !categoryIngredientIds.includes(id)
    );

    return modifications;
  }

  /**
   * Get effective ingredients for an item by applying modifications to category ingredients
   * @param {Object} item - Menu item
   * @param {Object} category - Menu category
   * @returns {Array<Object>} Effective ingredient objects
   */
  const getEffectiveIngredients = computed(() => {
    return (item, category) => {
      if (!item || !category) return [];

      // Get category ingredient IDs
      const categoryIngredientIds = category.categoryIngredients || [];

      // Apply item modifications
      const modifications = item.ingredients || { added: [], removed: [] };
      const effectiveIds = applyIngredientModifications(
        categoryIngredientIds,
        modifications
      );

      // Resolve IDs to full ingredient objects
      return resolveIngredientIds(effectiveIds);
    };
  });

  /**
   * Get resolved sizes for an item
   * @param {Object} item - Menu item
   * @returns {Array<Object>} Size objects with resolved data
   */
  const getResolvedSizes = computed(() => {
    return (item) => {
      if (!item || !item.sizes || !Array.isArray(item.sizes)) return [];

      return item.sizes
        .map((sizeInfo) => {
          const sizeObject = businessStore.getSizeOptionById(sizeInfo.sizeId);
          return {
            ...sizeInfo,
            name: sizeObject ? sizeObject.name : "Unknown Size",
            adjustmentType: sizeObject ? sizeObject.adjustmentType : "absolute",
            displayOrder: sizeObject ? sizeObject.displayOrder : 999,
          };
        })
        .sort((a, b) => (a.displayOrder || 999) - (b.displayOrder || 999));
    };
  });

  /**
   * Get resolved addons for a category or item
   * @param {Object} item - Menu item
   * @param {Object} category - Menu category
   * @returns {Array<Object>} Addon objects
   */
  const getResolvedAddons = computed(() => {
    return (item, category) => {
      if (!category || !category.categoryAddons) return [];

      // If item doesn't use category addons, return empty array
      if (item && item.addons && item.addons.useCategory === false) {
        return [];
      }

      // Start with category addons
      let effectiveAddons = [...category.categoryAddons];

      // If item has addon modifications, apply them
      if (item && item.addons) {
        // Remove addons that are in the removed list
        if (item.addons.removed && Array.isArray(item.addons.removed)) {
          effectiveAddons = effectiveAddons.filter(
            (addon) => !item.addons.removed.includes(addon.id)
          );
        }

        // Add addons that are in the added list
        if (item.addons.added && Array.isArray(item.addons.added)) {
          item.addons.added.forEach((addon) => {
            if (!effectiveAddons.find((a) => a.id === addon.id)) {
              effectiveAddons.push(addon);
            }
          });
        }
      }

      return effectiveAddons;
    };
  });

  return {
    // State
    menus,
    currentMenu,
    categories,
    items,
    loading,
    error,

    // Getters
    hasMenus,
    activeMenus,
    defaultMenu,
    getEffectiveIngredients,
    getResolvedSizes,
    getResolvedAddons,

    // Helper Methods
    resolveIngredientIds,
    resolveSizeIds,
    resolveUnitIds,
    resolveAddonIds,
    applyIngredientModifications,
    generateIngredientModifications,

    // Menu actions
    fetchBusinessMenus,
    getMenu,
    createMenu,
    updateMenu,
    deleteMenu,
    setCurrentMenu,
    clearCurrentMenu,

    // Category actions
    createCategory,
    updateCategory,
    deleteCategory,
    checkCategorySlugAvailability,

    // Item actions
    createMenuItem,
    updateMenuItem,
    deleteMenuItem,
  };
});
