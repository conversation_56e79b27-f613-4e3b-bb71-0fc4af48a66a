# Backend Migration Analysis: Supabase to ExpressJS + MongoDB

## Executive Summary

This document analyzes the feasibility, pros/cons, and requirements for migrating the TakeawayNZ application from Supabase to a custom ExpressJS server with MongoDB database.

## Current Architecture Assessment

### Current Supabase Integration

The application currently leverages Supabase extensively:

1. **Authentication System**: Complete JWT-based auth with role management
2. **Database**: PostgreSQL with Row Level Security (RLS) policies
3. **Real-time Features**: Built-in subscriptions and live updates
4. **Edge Functions**: Custom business logic (database reset functionality)
5. **Storage**: File storage for business logos and images
6. **Service Layer**: Well-structured service classes with automatic case conversion

### Current Database Schema

- **Core Tables**: users, user_profiles, businesses, business_users
- **Menu System**: menus, menu_categories, menu_items (recently implemented)
- **RBAC System**: user_system_roles, system_role_permissions, business_role_permissions
- **Advanced Features**: JSONB fields, array types, complex relationships

### Current Service Architecture

- **BaseDbService**: Abstract base class for CRUD operations
- **Specific Services**: UserService, BusinessService, MenuService
- **Helper Utilities**: supabaseHelper with automatic case conversion
- **Store Integration**: Pinia stores using service instances

## Migration Analysis

### Pros of Migration to ExpressJS + MongoDB

#### 1. **Full Control and Customization**

- Complete control over API design and business logic
- Custom middleware for authentication, validation, and logging
- Ability to implement complex business rules without database function limitations
- Custom caching strategies and performance optimizations

#### 2. **Cost Considerations**

- Potentially lower costs at scale (no per-request pricing)
- Predictable hosting costs with dedicated servers
- No vendor lock-in pricing changes

#### 3. **Technology Flexibility**

- Choice of deployment platforms (AWS, Google Cloud, Azure, self-hosted)
- Integration with any third-party services without API limitations
- Custom database optimization strategies
- Ability to implement microservices architecture

#### 4. **Advanced Features**

- Custom real-time implementations (Socket.io, WebSockets)
- Advanced caching with Redis
- Custom file processing and storage solutions
- Complex business logic without database function constraints

#### 5. **Development Experience**

- Full debugging capabilities in development
- Custom error handling and logging
- Ability to implement complex testing strategies
- No limitations on database queries or operations

### Cons of Migration to ExpressJS + MongoDB

#### 1. **Development Complexity**

- Significant increase in development time and effort
- Need to implement authentication system from scratch
- Manual implementation of real-time features
- Complex deployment and DevOps requirements

#### 2. **Infrastructure Management**

- Server maintenance and monitoring responsibilities
- Database backup and recovery procedures
- Security hardening and updates
- Scaling and load balancing considerations

#### 3. **Feature Parity Challenges**

- Loss of built-in RLS (need custom authorization)
- No automatic API generation
- Manual implementation of real-time subscriptions
- Custom file storage and CDN setup required

#### 4. **Time to Market**

- Estimated 4-6 weeks additional development time
- Potential delays in feature delivery
- Need for extensive testing of custom implementations

#### 5. **Maintenance Overhead**

- Ongoing server and database maintenance
- Security updates and patches
- Performance monitoring and optimization
- Backup and disaster recovery procedures

### Technical Debt Considerations

#### Current Strengths to Preserve

- Well-structured service layer architecture
- Automatic case conversion utilities
- Comprehensive error handling
- Debug logging system
- Store-service integration patterns

#### Potential Issues

- Loss of type safety from PostgreSQL to MongoDB
- Need to reimplement complex queries
- Manual transaction management
- Custom validation layer required

## Migration Requirements

### Phase 1: Infrastructure Setup (Week 1)

#### ExpressJS Server Setup

- **File**: `server/app.js` - Main Express application
- **File**: `server/config/database.js` - MongoDB connection configuration
- **File**: `server/config/environment.js` - Environment variable management
- **File**: `server/middleware/auth.js` - JWT authentication middleware
- **File**: `server/middleware/cors.js` - CORS configuration
- **File**: `server/middleware/validation.js` - Request validation middleware
- **File**: `server/middleware/errorHandler.js` - Global error handling

#### MongoDB Schema Design

- **File**: `server/models/User.js` - User model with Mongoose
- **File**: `server/models/Business.js` - Business model
- **File**: `server/models/Menu.js` - Menu system models
- **File**: `server/models/Order.js` - Order models (future)
- **File**: `server/schemas/validation.js` - Joi validation schemas

### Phase 2: Authentication System (Week 2)

#### JWT Implementation

- **File**: `server/services/AuthService.js` - Authentication logic
- **File**: `server/controllers/AuthController.js` - Auth endpoints
- **File**: `server/utils/jwt.js` - JWT token utilities
- **File**: `server/utils/password.js` - Password hashing utilities

#### Role-Based Access Control

- **File**: `server/middleware/rbac.js` - Role-based authorization
- **File**: `server/models/Role.js` - Role and permission models
- **File**: `server/services/PermissionService.js` - Permission checking logic

### Phase 3: Core API Implementation (Week 3-4)

#### Business Management API

- **File**: `server/controllers/BusinessController.js` - Business CRUD operations
- **File**: `server/services/BusinessService.js` - Business logic
- **File**: `server/routes/business.js` - Business routes

#### Menu Management API

- **File**: `server/controllers/MenuController.js` - Menu operations
- **File**: `server/services/MenuService.js` - Menu business logic
- **File**: `server/routes/menu.js` - Menu routes

#### User Management API

- **File**: `server/controllers/UserController.js` - User operations
- **File**: `server/services/UserService.js` - User business logic
- **File**: `server/routes/user.js` - User routes

### Phase 4: Frontend Integration (Week 5-6)

#### Service Layer Updates

- **Function**: Update `src/services/BaseDbService.js` - Replace Supabase calls with Axios
- **Function**: Update `src/services/UserService.js` - Implement custom auth methods
- **Function**: Update `src/services/BusinessService.js` - Update API endpoints
- **Function**: Update `src/services/MenuService.js` - Update menu API calls

#### Configuration Updates

- **Function**: Replace `src/boot/supabase.js` with `src/boot/api.js` - Axios configuration
- **Function**: Update `src/stores/auth.js` - Custom authentication store
- **Function**: Update environment variables in `.env` files

#### Utility Updates

- **Function**: Update `src/services/supabaseService.js` - Replace with API service
- **Function**: Maintain `src/utils/caseConversion.js` - Keep case conversion utilities
- **Function**: Update `src/utils/debug.js` - Enhance for API debugging

## Detailed File Modification Requirements

### Critical Frontend Files Requiring Updates

1. **Authentication Store** (`src/stores/auth.js`)

   - Replace Supabase auth methods with custom JWT implementation
   - Update session management for custom tokens
   - Implement custom role fetching logic
   - Add token refresh mechanism

2. **Service Layer** (`src/services/`)

   - **BaseDbService.js**: Replace supabaseHelper with Axios-based API calls
   - **UserService.js**: Implement custom authentication methods
   - **BusinessService.js**: Update business API endpoints
   - **MenuService.js**: Update menu management endpoints
   - **supabaseService.js**: Complete replacement with API service

3. **Boot Files** (`src/boot/`)

   - **supabase.js**: Replace with API configuration
   - Add new **api.js**: Axios setup with interceptors for auth

4. **Configuration Files**
   - **quasar.config.js**: Update environment variables
   - **.env files**: Replace Supabase URLs with API endpoints

### Backend Implementation Requirements

#### Core Server Structure

```
server/
├── app.js                 # Express app setup
├── server.js             # Server startup
├── config/
│   ├── database.js       # MongoDB connection
│   ├── environment.js    # Environment config
│   └── cors.js          # CORS configuration
├── middleware/
│   ├── auth.js          # JWT authentication
│   ├── rbac.js          # Role-based access
│   ├── validation.js    # Request validation
│   └── errorHandler.js  # Error handling
├── models/
│   ├── User.js          # User model
│   ├── Business.js      # Business model
│   ├── Menu.js          # Menu models
│   └── Role.js          # RBAC models
├── controllers/
│   ├── AuthController.js
│   ├── UserController.js
│   ├── BusinessController.js
│   └── MenuController.js
├── services/
│   ├── AuthService.js
│   ├── UserService.js
│   ├── BusinessService.js
│   └── MenuService.js
├── routes/
│   ├── auth.js
│   ├── users.js
│   ├── businesses.js
│   └── menus.js
└── utils/
    ├── jwt.js
    ├── password.js
    └── validation.js
```

## Risk Assessment

### High Risk Areas

1. **Authentication Security**: Custom JWT implementation requires careful security considerations
2. **Data Migration**: Complex data transformation from PostgreSQL to MongoDB
3. **Real-time Features**: Loss of built-in real-time capabilities
4. **Performance**: Need to optimize MongoDB queries for complex relationships

### Medium Risk Areas

1. **Development Timeline**: Potential for scope creep and delays
2. **Testing Coverage**: Need comprehensive testing of custom implementations
3. **Deployment Complexity**: Additional DevOps requirements

### Low Risk Areas

1. **Frontend Integration**: Well-structured service layer minimizes frontend changes
2. **Business Logic**: Most business logic can be preserved
3. **UI Components**: No changes required to Vue components

## Recommendation

### Current State Assessment

The application currently has a well-architected service layer that abstracts database operations effectively. The Supabase integration is comprehensive but not deeply embedded in the UI layer.

### Migration Feasibility: **MODERATE TO HIGH**

The migration is technically feasible due to:

- Well-structured service layer architecture
- Clear separation of concerns
- Comprehensive error handling and debugging systems

### Recommended Approach: **PHASED MIGRATION**

1. **Phase 1**: Set up parallel ExpressJS/MongoDB infrastructure
2. **Phase 2**: Implement authentication system with feature parity
3. **Phase 3**: Migrate core APIs (users, businesses, menus)
4. **Phase 4**: Update frontend services to use new APIs
5. **Phase 5**: Data migration and cutover
6. **Phase 6**: Decommission Supabase infrastructure

### Timeline Estimate: **6-8 weeks**

- Infrastructure setup: 1 week
- Authentication implementation: 1-2 weeks
- Core API development: 2-3 weeks
- Frontend integration: 1-2 weeks
- Testing and deployment: 1 week

### Cost-Benefit Analysis

- **Development Cost**: High (6-8 weeks additional development)
- **Operational Benefits**: Medium (better control, potential cost savings)
- **Technical Benefits**: High (full customization, no vendor lock-in)
- **Risk Level**: Medium (well-structured codebase reduces risk)

## Detailed Implementation Outline

### Backend Implementation Details

#### 1. Express Server Setup (`server/app.js`)

```javascript
// Core functionality to implement:
- Express application initialization
- Middleware stack setup (cors, helmet, compression)
- Route registration
- Error handling middleware
- Database connection initialization
- Environment-based configuration
```

#### 2. MongoDB Models (`server/models/`)

**User Model** (`User.js`)

```javascript
// Fields to implement:
- id (ObjectId)
- email (unique, required)
- username (unique, optional)
- phone (optional)
- phoneVerified (boolean)
- passwordHash (required)
- metadata (Object)
- createdAt, updatedAt (timestamps)
- Methods: comparePassword, generateJWT, toJSON
```

**Business Model** (`Business.js`)

```javascript
// Fields to implement:
- id (ObjectId)
- name, slug (unique)
- address fields (streetAddress, suburb, city, postCode)
- phone, email, gstNumber
- cuisineTypes (array)
- logoUrl, description
- isVerified, isActive, acceptsOnlineOrders
- defaultPrepTimeMinutes
- businessHours (object)
- businessHoursExceptions (array)
- sizeOptions, unitOptions (arrays)
- createdAt, updatedAt
```

**Menu Models** (`Menu.js`, `MenuCategory.js`, `MenuItem.js`)

```javascript
// Menu fields:
- id, businessId (ref to Business)
- name, slug, description
- isActive, isDefault
- availabilityStartTime, availabilityEndTime
- availabilityDays (array)
- displayOrder, deleted
- createdAt, updatedAt

// Category and Item fields similar to current schema
```

#### 3. Authentication System (`server/services/AuthService.js`)

```javascript
// Methods to implement:
- signUp(userData): Create user with hashed password
- signIn(email, password): Validate and return JWT
- refreshToken(token): Generate new JWT
- resetPassword(email): Send reset email
- updatePassword(userId, newPassword): Update password
- verifyEmail(token): Email verification
- generateJWT(user): Create signed token
- verifyJWT(token): Validate token
```

#### 4. RBAC Implementation (`server/middleware/rbac.js`)

```javascript
// Functionality to implement:
- Role checking middleware
- Permission validation
- Business-specific role checking
- Dynamic permission loading
- Role hierarchy support
```

### Frontend Service Updates

#### 1. API Service Replacement (`src/services/apiService.js`)

```javascript
// Replace supabaseService.js with:
- Axios instance with base configuration
- Request/response interceptors for auth
- Automatic case conversion (preserve existing utility)
- Error handling and retry logic
- Token refresh mechanism
```

#### 2. BaseDbService Updates (`src/services/BaseDbService.js`)

```javascript
// Methods to update:
- getAll(): Replace Supabase query with API call
- getById(): Update to use REST endpoint
- create(): Update to POST request
- update(): Update to PUT/PATCH request
- delete(): Update to DELETE request
- executeQuery(): Replace with custom API calls
```

#### 3. Authentication Store Updates (`src/stores/auth.js`)

```javascript
// Functions to update:
- initialize(): Check for stored JWT token
- signIn(): Call custom auth API
- signOut(): Clear local token and call logout API
- refreshToken(): Implement token refresh logic
- fetchUserRoles(): Call roles API endpoint
- Session management: Use localStorage/sessionStorage for JWT
```

### Data Migration Strategy

#### 1. Schema Mapping

```javascript
// PostgreSQL to MongoDB field mappings:
- UUID fields → ObjectId
- JSONB fields → Object type
- Array fields → Array type (preserve structure)
- Timestamp fields → Date type
- Text fields → String type
- Boolean fields → Boolean type (preserve)
```

#### 2. Data Export/Import Process

```javascript
// Steps to implement:
1. Export data from Supabase using existing services
2. Transform data structure for MongoDB
3. Handle relationship mapping (foreign keys → ObjectId refs)
4. Preserve data integrity during migration
5. Validate migrated data completeness
```

### API Endpoint Specifications

#### Authentication Endpoints

```
POST /api/auth/signup - User registration
POST /api/auth/signin - User login
POST /api/auth/signout - User logout
POST /api/auth/refresh - Token refresh
POST /api/auth/reset-password - Password reset request
PUT /api/auth/update-password - Password update
POST /api/auth/verify-email - Email verification
```

#### User Management Endpoints

```
GET /api/users/profile - Get user profile
PUT /api/users/profile - Update user profile
GET /api/users/roles - Get user roles
GET /api/users/permissions - Get user permissions
```

#### Business Management Endpoints

```
GET /api/businesses - Get user businesses
POST /api/businesses - Create business
GET /api/businesses/:id - Get business details
PUT /api/businesses/:id - Update business
DELETE /api/businesses/:id - Delete business
POST /api/businesses/:id/verify - Submit verification
GET /api/businesses/:id/permissions - Get business permissions
```

#### Menu Management Endpoints

```
GET /api/businesses/:businessId/menus - Get business menus
POST /api/businesses/:businessId/menus - Create menu
GET /api/menus/:id - Get menu details
PUT /api/menus/:id - Update menu
DELETE /api/menus/:id - Delete menu
GET /api/menus/:id/categories - Get menu categories
POST /api/menus/:id/categories - Create category
PUT /api/categories/:id - Update category
DELETE /api/categories/:id - Delete category
GET /api/categories/:id/items - Get category items
POST /api/categories/:id/items - Create menu item
PUT /api/items/:id - Update menu item
DELETE /api/items/:id - Delete menu item
```

### Testing Strategy

#### Backend Testing

```javascript
// Test files to create:
- server/tests/auth.test.js - Authentication tests
- server/tests/users.test.js - User management tests
- server/tests/businesses.test.js - Business tests
- server/tests/menus.test.js - Menu management tests
- server/tests/rbac.test.js - Role/permission tests
```

#### Frontend Testing

```javascript
// Test files to update:
-src / services / __tests__ / apiService.test.js -
  src / services / __tests__ / UserService.test.js -
  src / services / __tests__ / BusinessService.test.js -
  src / stores / __tests__ / auth.test.js;
```

### Deployment Configuration

#### Docker Setup

```dockerfile
# Files to create:
- Dockerfile (Node.js application)
- docker-compose.yml (App + MongoDB)
- .dockerignore
```

#### Environment Configuration

```javascript
// Environment variables to configure:
- NODE_ENV (development/production)
- MONGODB_URI (database connection)
- JWT_SECRET (token signing key)
- JWT_EXPIRES_IN (token expiration)
- CORS_ORIGIN (allowed origins)
- PORT (server port)
```

### Performance Considerations

#### Database Optimization

```javascript
// MongoDB indexes to create:
- User: email (unique), username (unique)
- Business: slug (unique), userId (compound)
- Menu: businessId + slug (compound)
- Category: menuId + displayOrder
- MenuItem: categoryId + displayOrder
```

#### Caching Strategy

```javascript
// Caching to implement:
- Redis for session storage
- Menu data caching
- User permission caching
- Business data caching
```

## Conclusion

The migration from Supabase to ExpressJS + MongoDB is feasible but represents a significant undertaking. The current architecture's service layer provides good abstraction that will facilitate the migration. However, the benefits must be weighed against the substantial development effort required.

**Recommendation**: Proceed with migration only if there are compelling business reasons (cost, specific feature requirements, or vendor independence concerns) that justify the development investment.

### Key Success Factors

1. **Preserve Service Architecture**: Maintain the current service layer pattern
2. **Incremental Migration**: Implement and test each component separately
3. **Comprehensive Testing**: Ensure feature parity through extensive testing
4. **Data Integrity**: Careful planning and validation of data migration
5. **Performance Monitoring**: Establish monitoring from day one

### Alternative Recommendation

Consider staying with Supabase unless there are compelling technical or business reasons for migration. The current implementation is well-architected and provides excellent developer experience with minimal operational overhead.
