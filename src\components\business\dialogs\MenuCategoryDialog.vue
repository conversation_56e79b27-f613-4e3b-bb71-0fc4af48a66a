<template>
  <q-dialog
    ref="dialogRef"
    persistent
    :maximized="$q.screen.lt.sm"
    transition-show="scale"
    transition-hide="scale"
    @hide="onDialogHide"
  >
    <q-card class="dialog-card">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">{{ title }}</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-separator class="q-my-sm" />

      <q-card-section class="q-pt-none">
        <q-form @submit="onSubmit" class="q-gutter-md">
          <!-- Category Name -->
          <q-input
            v-model="form.name"
            :label="$t('business.menu.categoryName')"
            :rules="[
              (val) =>
                !!val ||
                $t('validation.required', {
                  field: $t('business.menu.categoryName'),
                }),
            ]"
            outlined
            dense
            autofocus
          />

          <!-- Category Description -->
          <q-input
            v-model="form.description"
            :label="$t('business.menu.categoryDescription')"
            type="textarea"
            outlined
            dense
            autogrow
          />

          <!-- Category Ingredients -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-xs">
              {{ $t("business.menu.categoryIngredients") }}
            </div>
            <div class="text-caption q-mb-sm">
              {{ $t("business.menu.categoryIngredientsHint") }}
            </div>

            <div class="row q-col-gutter-sm">
              <div class="col-12">
                <div class="row q-col-gutter-sm">
                  <div class="col">
                    <q-select
                      v-model="selectedIngredients"
                      :options="availableIngredients"
                      option-value="id"
                      option-label="name"
                      :label="$t('business.menu.selectIngredients')"
                      outlined
                      dense
                      multiple
                      use-chips
                      use-input
                      input-debounce="0"
                      @filter="filterIngredients"
                      @update:model-value="updateCategoryIngredients"
                      class="q-mb-sm"
                    >
                      <template v-slot:option="scope">
                        <q-item v-bind="scope.itemProps">
                          <q-item-section>
                            <q-item-label>{{ scope.opt.name }}</q-item-label>
                            <q-item-label caption>{{
                              scope.opt.category
                            }}</q-item-label>
                          </q-item-section>
                        </q-item>
                      </template>

                      <template v-slot:no-option>
                        <q-item>
                          <q-item-section class="text-grey">
                            {{ $t("business.menu.noIngredientsFound") }}
                          </q-item-section>
                        </q-item>
                      </template>

                      <template v-slot:selected-item="scope">
                        <q-chip
                          removable
                          dense
                          @remove="scope.removeAtIndex(scope.index)"
                          :tabindex="scope.tabindex"
                          color="primary"
                          text-color="white"
                        >
                          {{ scope.opt.name }}
                        </q-chip>
                      </template>
                    </q-select>
                  </div>
                  <div class="col-auto self-center">
                    <q-btn
                      round
                      dense
                      color="primary"
                      icon="add"
                      @click="openNewIngredientDialog"
                      class="q-mb-sm"
                    >
                      <q-tooltip>{{
                        $t("business.menu.addNewIngredient")
                      }}</q-tooltip>
                    </q-btn>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Category Addons -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-xs">
              {{ $t("business.menu.categoryAddons") }}
            </div>
            <div class="text-caption q-mb-sm">
              {{ $t("business.menu.categoryAddonsHint") }}
            </div>

            <div v-if="form.categoryAddons.length > 0" class="q-mb-sm">
              <q-chip
                v-for="(addon, index) in form.categoryAddons"
                :key="index"
                removable
                @remove="removeAddon(index)"
                color="positive"
                text-color="white"
                class="q-ma-xs"
              >
                {{ addon.name }}
                <q-badge color="white" text-color="positive" class="q-ml-sm">
                  ${{ addon.price.toFixed(2) }}
                </q-badge>
              </q-chip>
            </div>

            <q-card flat bordered class="q-pa-sm">
              <div class="text-caption q-mb-sm">
                {{ $t("business.menu.addNewAddon") }}
              </div>

              <div class="row q-col-gutter-sm">
                <div class="row col-12">
                  <div class="col-8">
                    <q-input
                      v-model="newAddon.name"
                      :label="$t('business.menu.addonName')"
                      outlined
                      dense
                      class="q-mb-sm q-mr-sm"
                    />
                  </div>
                  <div class="col-4">
                    <q-input
                      v-model.number="newAddon.price"
                      :label="$t('business.menu.addonPrice')"
                      type="number"
                      step="0.25"
                      min="0"
                      prefix="$"
                      mask="#.##"
                      fill-mask="0"
                      reverse-fill-mask
                      outlined
                      dense
                      class="q-mb-sm"
                    >
                      <template v-slot:prepend>
                        <q-icon name="paid" />
                      </template>
                    </q-input>
                  </div>
                </div>
                <div class="col-12">
                  <q-input
                    v-model="newAddon.description"
                    :label="$t('business.menu.addonDescription')"
                    outlined
                    dense
                    class="q-mb-sm"
                  />
                </div>
              </div>

              <q-btn
                :label="$t('business.menu.addAddon')"
                color="positive"
                icon="add"
                :disable="!newAddon.name || !newAddon.price"
                @click="addAddon"
              />
            </q-card>
          </div>

          <!-- Availability Settings -->
          <q-expansion-item
            :label="$t('business.menu.availabilitySettings')"
            icon="schedule"
            header-class="text-primary"
            expand-icon-class="text-primary"
          >
            <q-card>
              <q-card-section>
                <q-toggle
                  v-model="form.isAvailable"
                  :label="$t('business.menu.categoryAvailable')"
                  color="positive"
                  class="q-mb-md"
                />

                <div class="row q-col-gutter-md" v-if="form.isAvailable">
                  <div class="col-6">
                    <q-input
                      v-model="form.availabilityStartTime"
                      :label="
                        $t('business.menu.settings.availability.startTime')
                      "
                      type="time"
                      step="00:15"
                      outlined
                      dense
                    />
                  </div>
                  <div class="col-6">
                    <q-input
                      v-model="form.availabilityEndTime"
                      :label="$t('business.menu.settings.availability.endTime')"
                      type="time"
                      step="900"
                      outlined
                      dense
                    />
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </q-expansion-item>

          <!-- Action Buttons -->
          <div class="row justify-end q-mt-md">
            <q-btn
              :label="$t('common.cancel')"
              color="grey-7"
              v-close-popup
              flat
              class="q-mr-sm"
            />
            <q-btn
              :label="$t('common.save')"
              color="primary"
              type="submit"
              :loading="loading"
            />
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </q-dialog>

  <!-- Ingredients Dialog -->
  <ingredients-dialog
    v-model="showIngredientsDialog"
    :ingredient="editingIngredient"
    @submit="handleIngredientSubmit"
    @get-categories="provideCategories"
    ref="ingredientsDialogRef"
  />
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { useDialogPluginComponent, useQuasar } from "quasar";
import { createDebugger } from "src/utils/debug";
import { useBusinessStore } from "src/stores/business";
import {
  generateIngredientId,
  generateAddonId,
  isIngredientId,
} from "src/utils/idGenerator";
import IngredientsDialog from "src/components/business/dialogs/IngredientsDialog.vue";

const debug = createDebugger("component:menu-category-dialog");
const { t } = useI18n();
const businessStore = useBusinessStore();
const $q = useQuasar();

// Dialog plugin setup
const { dialogRef, onDialogHide, onDialogOK, onDialogCancel } =
  useDialogPluginComponent();

// Props
const props = defineProps({
  category: {
    type: Object,
    default: () => ({
      id: null,
      name: "",
      description: "",
      isAvailable: true,
      availabilityStartTime: "",
      availabilityEndTime: "",
      categoryIngredients: [],
      categoryAddons: [],
    }),
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

// State
const loading = ref(false);
const form = ref({
  id: props.category.id,
  name: props.category.name,
  description: props.category.description || "",
  isAvailable:
    props.category.isAvailable !== undefined
      ? props.category.isAvailable
      : true,
  availabilityStartTime: props.category.availabilityStartTime || "",
  availabilityEndTime: props.category.availabilityEndTime || "",
  categoryIngredients: props.category.categoryIngredients
    ? [...props.category.categoryIngredients]
    : [],
  categoryAddons: props.category.categoryAddons
    ? [...props.category.categoryAddons]
    : [],
});

// New ingredient and addon state
const selectedIngredients = ref([]);
const newAddon = ref({ name: "", price: null, description: "" });

// Ingredients dialog state
const showIngredientsDialog = ref(false);
const editingIngredient = ref(null);
const ingredientsDialogRef = ref(null);

// Business ingredients
const businessIngredients = computed(() => {
  if (
    businessStore.currentBusiness &&
    businessStore.currentBusiness.ingredients
  ) {
    return businessStore.currentBusiness.ingredients;
  }
  // Fallback to some default ingredients if none are defined in the business
  return [
    { id: "i-def01", name: "Lettuce", category: "vegetable" },
    { id: "i-def02", name: "Tomato", category: "vegetable" },
    { id: "i-def03", name: "Onion", category: "vegetable" },
    { id: "i-def04", name: "Cheese", category: "dairy" },
    { id: "i-def05", name: "Chicken", category: "protein" },
    { id: "i-def06", name: "Beef", category: "protein" },
    { id: "i-def07", name: "Garlic", category: "spice" },
    { id: "i-def08", name: "Salt", category: "spice" },
    { id: "i-def09", name: "Pepper", category: "spice" },
  ];
});

// All available ingredients from the business
const availableIngredients = computed(() => {
  return businessIngredients.value;
});

// Filtered ingredients for search
const filteredIngredients = ref([]);

// Computed
const title = computed(() => {
  return props.isEdit
    ? t("business.menu.editCategory")
    : t("business.menu.addCategory");
});

// Methods
function filterIngredients(val, update) {
  if (val === "") {
    update(() => {
      filteredIngredients.value = availableIngredients.value;
    });
    return;
  }

  const needle = val.toLowerCase();
  update(() => {
    filteredIngredients.value = availableIngredients.value.filter(
      (v) =>
        v.name.toLowerCase().indexOf(needle) > -1 ||
        v.category.toLowerCase().indexOf(needle) > -1
    );
  });
}

// Update the category ingredients when the multi-select changes
function updateCategoryIngredients() {
  // Clear the current category ingredients
  form.value.categoryIngredients = [];

  // Add all selected ingredient IDs to the category ingredients
  if (selectedIngredients.value && selectedIngredients.value.length > 0) {
    selectedIngredients.value.forEach((ingredient) => {
      // Store only the ingredient ID, not the full object
      form.value.categoryIngredients.push(ingredient.id);
    });
  }

  debug("Updated category ingredients:", form.value.categoryIngredients);
}

// Initialize selected ingredients from existing category ingredients
function initializeSelectedIngredients() {
  if (
    form.value.categoryIngredients &&
    form.value.categoryIngredients.length > 0
  ) {
    selectedIngredients.value = [];

    form.value.categoryIngredients.forEach((ingredientIdOrObj) => {
      let ingredient = null;

      // Handle both ID strings and ingredient objects for backward compatibility
      if (typeof ingredientIdOrObj === "string") {
        // It's an ID, find the ingredient in business ingredients
        ingredient = businessIngredients.value.find(
          (ing) => ing.id === ingredientIdOrObj
        );
      } else if (
        typeof ingredientIdOrObj === "object" &&
        ingredientIdOrObj.id
      ) {
        // It's an object, find the ingredient by ID or use the object itself
        ingredient =
          businessIngredients.value.find(
            (ing) => ing.id === ingredientIdOrObj.id
          ) || ingredientIdOrObj;
      }

      if (ingredient) {
        selectedIngredients.value.push(ingredient);
      } else {
        debug(
          `Could not find ingredient for: ${JSON.stringify(ingredientIdOrObj)}`
        );
      }
    });

    debug("Initialized selected ingredients:", selectedIngredients.value);
  }
}

// Note: We no longer need this function as removal is handled by the multi-select
// function removeIngredient(index) {
//   form.value.categoryIngredients.splice(index, 1);
// }

function addAddon() {
  if (!newAddon.value.name || !newAddon.value.price) return;

  form.value.categoryAddons.push({
    id: generateAddonId(), // Generate proper addon ID
    name: newAddon.value.name,
    price: parseFloat(newAddon.value.price) || 0,
    description: newAddon.value.description || "",
  });

  // Reset form
  newAddon.value = { name: "", price: 0, description: "" };
}

function removeAddon(index) {
  form.value.categoryAddons.splice(index, 1);
}

function onSubmit() {
  debug("Submitting form", form.value);
  loading.value = true;

  // Generate slug from name if not editing
  if (!props.isEdit) {
    form.value.slug = form.value.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-|-$/g, "");
  }

  // Prepare the data for submission
  const categoryData = {
    ...form.value,
  };

  // Remove id field for new categories (let database auto-generate)
  if (!props.isEdit) {
    delete categoryData.id;
  }

  // Convert empty time strings to null for database compatibility
  if (
    !categoryData.availabilityStartTime ||
    categoryData.availabilityStartTime === ""
  ) {
    categoryData.availabilityStartTime = null;
  }
  if (
    !categoryData.availabilityEndTime ||
    categoryData.availabilityEndTime === ""
  ) {
    categoryData.availabilityEndTime = null;
  }

  debug("Category data being sent:", categoryData);

  // Return the form data to the parent component
  onDialogOK(categoryData);

  // Reset loading state (in case dialog is not closed)
  setTimeout(() => {
    loading.value = false;
  }, 500);
}

// Open the ingredients dialog to add a new ingredient
function openNewIngredientDialog() {
  debug("Opening ingredients dialog");
  editingIngredient.value = null; // Reset editing state
  showIngredientsDialog.value = true;
}

// Provide categories to the ingredients dialog
function provideCategories() {
  debug("Providing categories to ingredients dialog");

  // Extract unique categories from business ingredients
  const categories = businessIngredients.value
    .map((ing) => ing.category)
    .filter((value, index, self) => self.indexOf(value) === index);

  debug("Categories to provide:", categories);

  // Set categories in the dialog
  if (ingredientsDialogRef.value) {
    ingredientsDialogRef.value.setCategories(categories);
  }
}

// Handle ingredient submission from the dialog
function handleIngredientSubmit(ingredient) {
  debug("Handling ingredient submission:", ingredient);
  debug("Current business:", businessStore.currentBusiness);
  debug(
    "Current business ingredients:",
    businessStore.currentBusiness?.ingredients
  );

  // Ensure the ingredient has an ID
  if (!ingredient.id) {
    ingredient.id = generateIngredientId();
    debug("Generated new ingredient ID:", ingredient.id);
  }

  // Check if ingredient already exists
  const existingIngredient = businessIngredients.value.find(
    (ing) => ing.name.toLowerCase() === ingredient.name.toLowerCase()
  );

  debug("Existing ingredient found:", existingIngredient);

  if (!existingIngredient) {
    // Add to business ingredients using the store method
    try {
      debug("Adding ingredient to business store:", ingredient);
      const newId = businessStore.addIngredient(ingredient);
      debug("Ingredient added with ID:", newId);

      // Update the ingredient with the returned ID
      ingredient.id = newId;

      // Select the new ingredient
      selectedIngredients.value.push(ingredient);
      updateCategoryIngredients();

      debug("Updated selected ingredients:", selectedIngredients.value);
      debug("Updated category ingredients:", form.value.categoryIngredients);

      // Show success notification
      $q.notify({
        type: "positive",
        message: t("business.menu.ingredientAdded"),
        icon: "check_circle",
      });
    } catch (error) {
      debug("Error adding ingredient to business:", error);
      $q.notify({
        type: "negative",
        message: t("business.menu.ingredientAddError"),
        icon: "error",
      });
    }
  } else {
    // Update existing ingredient if category changed
    if (existingIngredient.category !== ingredient.category) {
      try {
        businessStore.updateIngredient(existingIngredient.id, {
          category: ingredient.category,
        });

        // Show update notification
        $q.notify({
          type: "positive",
          message: t("business.menu.ingredientUpdated"),
          icon: "check_circle",
        });
      } catch (error) {
        debug("Error updating ingredient:", error);
        $q.notify({
          type: "negative",
          message: t("business.menu.ingredientUpdateError"),
          icon: "error",
        });
      }
    } else {
      // Show warning notification
      $q.notify({
        type: "warning",
        message: t("business.menu.ingredientAlreadyExists"),
        icon: "warning",
      });
    }
  }
}

// Lifecycle
onMounted(() => {
  debug("Dialog mounted", { props });

  // Initialize selected ingredients from existing category ingredients
  initializeSelectedIngredients();
});
</script>

<style lang="scss" scoped>
.dialog-card {
  min-width: 400px;
  max-width: 600px;
  width: 90vw;
}

@media (max-width: 599px) {
  .dialog-card {
    width: 100%;
  }
}
</style>
