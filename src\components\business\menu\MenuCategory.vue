<template>
  <q-expansion-item
    class="category-item"
    :label="category.name"
    :caption="category.description || ''"
    :default-opened="false"
    header-class="category-header"
    expand-icon-class="text-primary"
  >
    <template v-slot:header>
      <q-item-section>
        <div class="row items-center">
          <div class="col">
            <q-item-label class="text-weight-medium">{{
              category.name
            }}</q-item-label>
            <q-item-label caption v-if="category.description">{{
              category.description
            }}</q-item-label>
          </div>
          <div class="col-auto">
            <q-badge color="grey" text-color="white" class="q-mr-sm">
              {{ category.items ? category.items.length : 0 }}
              {{ $t("business.menu.itemsCount") }}
            </q-badge>
            <q-btn-group flat>
              <q-btn
                flat
                dense
                icon="edit"
                color="primary"
                @click.stop="editCategory"
              >
                <q-tooltip>{{ $t("common.edit") }}</q-tooltip>
              </q-btn>
              <q-btn
                flat
                dense
                icon="add"
                color="positive"
                @click.stop="addMenuItem"
              >
                <q-tooltip>{{
                  $t("business.menu.addItemToCategory")
                }}</q-tooltip>
              </q-btn>
              <q-btn
                flat
                dense
                icon="delete"
                color="negative"
                @click.stop="deleteCategory"
              >
                <q-tooltip>{{ $t("common.delete") }}</q-tooltip>
              </q-btn>
            </q-btn-group>
          </div>
        </div>
      </q-item-section>
    </template>

    <!-- Category Ingredients and Addons -->
    <div class="category-options q-px-md q-py-sm" v-if="hasCategoryOptions">
      <div class="row q-col-gutter-md">
        <!-- Category Ingredients -->
        <div class="col-12 col-md-6" v-if="hasCategoryIngredients">
          <div class="text-subtitle2 q-mb-xs">
            <q-icon name="remove_circle_outline" size="xs" class="q-mr-xs" />
            {{ $t("business.menu.categoryIngredients") }}
          </div>
          <div class="row q-gutter-xs">
            <q-chip
              v-for="(ingredient, index) in resolvedCategoryIngredients"
              :key="ingredient.id || index"
              dense
              outline
              color="primary"
              icon="remove_circle_outline"
            >
              {{ ingredient.name }}
            </q-chip>
          </div>
          <div class="text-caption text-grey-7 q-mt-xs">
            {{ $t("business.menu.canBeRemoved") }}
          </div>
        </div>

        <!-- Category Addons -->
        <div class="col-12 col-md-6" v-if="hasCategoryAddons">
          <div class="text-subtitle2 q-mb-xs">
            <q-icon name="add_circle_outline" size="xs" class="q-mr-xs" />
            {{ $t("business.menu.categoryAddons") }}
          </div>
          <div class="row q-gutter-xs">
            <q-chip
              v-for="(addon, index) in category.categoryAddons"
              :key="index"
              dense
              outline
              color="positive"
              icon="add_circle_outline"
            >
              {{ addon.name }}
              <q-badge color="positive" class="q-ml-xs">
                ${{ addon.price.toFixed(2) }}
              </q-badge>
              <q-tooltip>
                {{ addon.description }}
              </q-tooltip>
            </q-chip>
          </div>
        </div>
      </div>
    </div>

    <!-- Menu Items -->
    <q-list separator class="menu-items-list q-pl-md">
      <MenuItem
        v-for="item in category.items"
        :key="item.id"
        :item="item"
        :category-id="category.id"
        @edit-item="onEditItem"
        @duplicate-item="onDuplicateItem"
        @delete-item="onDeleteItem"
      />

      <!-- Empty state for no items -->
      <q-item
        v-if="!category.items || category.items.length === 0"
        class="text-center"
      >
        <q-item-section>
          <p class="text-grey-7 q-my-sm">
            {{ $t("business.menu.noCategoryItems") }}
          </p>
          <q-btn
            color="positive"
            icon="add"
            :label="$t('business.menu.addItemToCategory')"
            size="sm"
            @click="addMenuItem"
          />
        </q-item-section>
      </q-item>
    </q-list>
  </q-expansion-item>
</template>

<script setup>
import { createDebugger } from "src/utils/debug";
import { useI18n } from "vue-i18n";
import { computed, ref } from "vue";
import { useQuasar } from "quasar";
import { useBusinessStore } from "src/stores/business";
import MenuItem from "./MenuItem.vue";
import MenuCategoryDialog from "../dialogs/MenuCategoryDialog.vue";

const debug = createDebugger("component:menu-category");
const { t } = useI18n(); // Used for translations
const $q = useQuasar();
const businessStore = useBusinessStore();

// Props
const props = defineProps({
  category: {
    type: Object,
    required: true,
  },
});

// Computed properties
const hasCategoryIngredients = computed(() => {
  return (
    props.category.categoryIngredients &&
    props.category.categoryIngredients.length > 0
  );
});

// Get business ingredients for resolving IDs
const businessIngredients = computed(() => {
  if (
    businessStore.currentBusiness &&
    businessStore.currentBusiness.ingredients
  ) {
    return businessStore.currentBusiness.ingredients;
  }
  return [];
});

// Resolve category ingredient IDs to ingredient objects
const resolvedCategoryIngredients = computed(() => {
  if (
    !props.category.categoryIngredients ||
    !businessIngredients.value.length
  ) {
    return [];
  }

  return props.category.categoryIngredients
    .map((ingredientId) => {
      // If it's already an object, return it
      if (typeof ingredientId === "object" && ingredientId.name) {
        return ingredientId;
      }

      // If it's an ID string, resolve it
      if (typeof ingredientId === "string") {
        const ingredient = businessIngredients.value.find(
          (ing) => ing.id === ingredientId
        );
        if (ingredient) {
          return ingredient;
        }
        // If not found, create a placeholder
        debug(
          `Ingredient with ID ${ingredientId} not found in business ingredients`
        );
        return {
          id: ingredientId,
          name: `Unknown (${ingredientId})`,
          category: "unknown",
        };
      }

      return null;
    })
    .filter(Boolean);
});

const hasCategoryAddons = computed(() => {
  return (
    props.category.categoryAddons && props.category.categoryAddons.length > 0
  );
});

const hasCategoryOptions = computed(() => {
  return hasCategoryIngredients.value || hasCategoryAddons.value;
});

// Emits
const emit = defineEmits([
  "edit-category",
  "add-menu-item",
  "delete-category",
  "edit-item",
  "duplicate-item",
  "delete-item",
]);

// Methods
function editCategory() {
  debug(`Edit category clicked: ${props.category.id}`);

  $q.dialog({
    component: MenuCategoryDialog,
    componentProps: {
      category: props.category,
      isEdit: true,
    },
  })
    .onOk((updatedCategory) => {
      debug("Category updated:", updatedCategory);
      emit("edit-category", updatedCategory);
    })
    .onCancel(() => {
      debug("Edit category cancelled");
    })
    .onDismiss(() => {
      debug("Edit category dismissed");
    });
}

function addMenuItem() {
  debug(`Add menu item clicked for category: ${props.category.id}`);
  emit("add-menu-item", props.category.id);
}

function deleteCategory() {
  debug(`Delete category clicked: ${props.category.id}`);
  emit("delete-category", props.category.id);
}

// Item event handlers
function onEditItem(itemId, categoryId) {
  debug(`Edit item event received: ${itemId} in category ${categoryId}`);
  emit("edit-item", itemId, categoryId);
}

function onDuplicateItem(itemId, categoryId) {
  debug(`Duplicate item event received: ${itemId} in category ${categoryId}`);
  emit("duplicate-item", itemId, categoryId);
}

function onDeleteItem(itemId, categoryId) {
  debug(`Delete item event received: ${itemId} in category ${categoryId}`);
  emit("delete-item", itemId, categoryId);
}
</script>

<style lang="scss" scoped>
.category-item {
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.03);
  }

  .category-header {
    padding: 12px 16px;
  }
}

.menu-items-list {
  background-color: rgba(0, 0, 0, 0.02);
}

.category-options {
  background-color: rgba(0, 0, 0, 0.01);
  border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
}

.q-btn-group {
  opacity: 0.7;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
  }
}
</style>
