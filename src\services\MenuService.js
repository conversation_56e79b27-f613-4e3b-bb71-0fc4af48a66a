/**
 * Menu Service
 *
 * Service for handling menu-related database operations.
 */

import { BaseDbService } from "./BaseDbService";
import { createDebugger } from "src/utils/debug";

export class MenuService extends BaseDbService {
  constructor() {
    super("menus", "menu");
    this.debug = createDebugger("services:menu");
    this.menuCategoriesService = new MenuCategoriesService();
    this.menuItemsService = new MenuItemsService();
  }

  /**
   * Get all menus for a business
   * @param {string} businessId - The business ID
   * @param {boolean} includeDeleted - Whether to include deleted menus
   * @returns {Promise<Object>} The menus
   */
  async getBusinessMenus(businessId, includeDeleted = false) {
    this.debug(`Getting menus for business: ${businessId}`);
    try {
      const options = {
        filters: {
          businessId,
          ...(includeDeleted ? {} : { deleted: false }),
        },
        orderBy: "displayOrder",
      };
      return await this.getAll("*", options);
    } catch (error) {
      this.debug(`Error getting menus for business: %O`, error);
      throw error;
    }
  }

  /**
   * Get a menu by ID or slug
   * @param {string} menuIdOrSlug - The menu ID or slug
   * @param {string} businessId - The business ID (required if using slug)
   * @returns {Promise<Object>} The menu
   */
  async getMenu(menuIdOrSlug, businessId = null) {
    this.debug(`Getting menu: ${menuIdOrSlug}`);
    try {
      if (this.isUuid(menuIdOrSlug)) {
        return await this.getById(menuIdOrSlug);
      } else if (businessId) {
        // If it's a slug, we need the business ID to ensure uniqueness
        const options = {
          filters: {
            businessId,
            slug: menuIdOrSlug,
            deleted: false,
          },
        };
        const { data } = await this.getAll("*", options);
        return { data: data[0] || null, error: null };
      } else {
        throw new Error("Business ID is required when getting menu by slug");
      }
    } catch (error) {
      this.debug(`Error getting menu: %O`, error);
      throw error;
    }
  }

  /**
   * Create a new menu
   * @param {Object} menuData - The menu data
   * @returns {Promise<Object>} The created menu
   */
  async createMenu(menuData) {
    this.debug(`Creating menu for business: ${menuData.businessId}`);
    try {
      return await this.create(menuData);
    } catch (error) {
      this.debug(`Error creating menu: %O`, error);
      throw error;
    }
  }

  /**
   * Update a menu
   * @param {string} menuId - The menu ID
   * @param {Object} updates - The updates to apply
   * @returns {Promise<Object>} The updated menu
   */
  async updateMenu(menuId, updates) {
    this.debug(`Updating menu: ${menuId}`);
    try {
      return await this.update(updates, { id: menuId });
    } catch (error) {
      this.debug(`Error updating menu: %O`, error);
      throw error;
    }
  }

  /**
   * Soft delete a menu
   * @param {string} menuId - The menu ID
   * @returns {Promise<Object>} The result of the operation
   */
  async deleteMenu(menuId) {
    this.debug(`Soft deleting menu: ${menuId}`);
    try {
      return await this.update({ deleted: true }, { id: menuId });
    } catch (error) {
      this.debug(`Error deleting menu: %O`, error);
      throw error;
    }
  }

  /**
   * Get a complete menu with categories and items
   * @param {string} menuIdOrSlug - The menu ID or slug
   * @param {string} businessId - The business ID (required if using slug)
   * @returns {Promise<Object>} The complete menu with categories and items
   */
  async getCompleteMenu(menuIdOrSlug, businessId = null) {
    this.debug(`Getting complete menu: ${menuIdOrSlug}`);
    try {
      // Get the menu
      const { data: menu } = await this.getMenu(menuIdOrSlug, businessId);
      if (!menu) {
        throw new Error(`Menu with ID/slug '${menuIdOrSlug}' not found`);
      }

      // Get categories for this menu
      const { data: categories } =
        await this.menuCategoriesService.getMenuCategories(menu.id);

      // Get items for this menu
      const { data: items } = await this.menuItemsService.getMenuItems(menu.id);

      // Organize items by category
      const categoriesWithItems = categories.map((category) => {
        const categoryItems = items.filter(
          (item) => item.categoryId === category.id
        );
        return {
          ...category,
          items: categoryItems,
        };
      });

      return {
        data: {
          ...menu,
          categories: categoriesWithItems,
        },
        error: null,
      };
    } catch (error) {
      this.debug(`Error getting complete menu: %O`, error);
      throw error;
    }
  }

  /**
   * Check if a string is a UUID
   * @param {string} str - The string to check
   * @returns {boolean} Whether the string is a UUID
   */
  isUuid(str) {
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
  }
}

/**
 * Menu Categories Service
 *
 * Service for handling menu category-related database operations.
 */
export class MenuCategoriesService extends BaseDbService {
  constructor() {
    super("menu_categories", "menu_categories");
    this.debug = createDebugger("services:menu_categories");
  }

  /**
   * Get all categories for a menu
   * @param {string} menuId - The menu ID
   * @param {boolean} includeDeleted - Whether to include deleted categories
   * @returns {Promise<Object>} The categories
   */
  async getMenuCategories(menuId, includeDeleted = false) {
    this.debug(`Getting categories for menu: ${menuId}`);
    try {
      const options = {
        filters: {
          menuId,
          ...(includeDeleted ? {} : { deleted: false }),
        },
        orderBy: "displayOrder",
      };
      return await this.getAll("*", options);
    } catch (error) {
      this.debug(`Error getting categories for menu: %O`, error);
      throw error;
    }
  }

  /**
   * Get a category by ID
   * @param {string} categoryId - The category ID
   * @returns {Promise<Object>} The category
   */
  async getCategory(categoryId) {
    this.debug(`Getting category: ${categoryId}`);
    try {
      return await this.getById(categoryId);
    } catch (error) {
      this.debug(`Error getting category: %O`, error);
      throw error;
    }
  }

  /**
   * Create a new category
   * @param {Object} categoryData - The category data
   * @returns {Promise<Object>} The created category
   */
  async createCategory(categoryData) {
    this.debug(`Creating category for menu: ${categoryData.menuId}`);
    try {
      return await this.create(categoryData);
    } catch (error) {
      this.debug(`Error creating category: %O`, error);
      throw error;
    }
  }

  /**
   * Update a category
   * @param {string} categoryId - The category ID
   * @param {Object} updates - The updates to apply
   * @returns {Promise<Object>} The updated category
   */
  async updateCategory(categoryId, updates) {
    this.debug(`Updating category: ${categoryId}`);
    try {
      return await this.update(updates, { id: categoryId });
    } catch (error) {
      this.debug(`Error updating category: %O`, error);
      throw error;
    }
  }

  /**
   * Soft delete a category
   * @param {string} categoryId - The category ID
   * @returns {Promise<Object>} The result of the operation
   */
  async deleteCategory(categoryId) {
    this.debug(`Soft deleting category: ${categoryId}`);
    try {
      // First get the category to append timestamp to slug
      const { data: category } = await this.getById(categoryId);
      if (!category) {
        throw new Error(`Category with ID ${categoryId} not found`);
      }

      // Append timestamp to slug to avoid unique constraint issues
      const timestamp = Date.now();
      const updatedSlug = `${category.slug}-deleted-${timestamp}`;

      return await this.update(
        {
          deleted: true,
          slug: updatedSlug,
        },
        { id: categoryId }
      );
    } catch (error) {
      this.debug(`Error deleting category: %O`, error);
      throw error;
    }
  }

  /**
   * Check if a category slug is available within a menu
   * @param {string} menuId - The menu ID
   * @param {string} slug - The slug to check
   * @param {string} [excludeCategoryId] - Category ID to exclude from check (for editing)
   * @returns {Promise<Object>} Object with available flag
   */
  async checkCategorySlugAvailability(menuId, slug, excludeCategoryId = null) {
    this.debug(
      `Checking category slug availability: ${slug} in menu ${menuId}`
    );
    try {
      let filters = {
        menu_id: menuId,
        slug: slug,
        deleted: false, // Only check non-deleted categories
      };

      // Exclude current category when editing
      if (excludeCategoryId) {
        filters = {
          ...filters,
          id: `neq.${excludeCategoryId}`, // Supabase syntax for "not equal"
        };
      }

      const { data } = await this.getAll("id", { filters });

      // If no data or empty array, slug is available
      const isAvailable = !data || data.length === 0;
      this.debug(
        `Category slug '${slug}' is ${
          isAvailable ? "available" : "taken"
        } in menu ${menuId}`
      );
      return { available: isAvailable };
    } catch (error) {
      this.debug(`Error checking category slug availability: %O`, error);
      throw error;
    }
  }
}

/**
 * Menu Items Service
 *
 * Service for handling menu item-related database operations.
 */
export class MenuItemsService extends BaseDbService {
  constructor() {
    super("menu_items", "menu_items");
    this.debug = createDebugger("services:menu_items");
  }

  /**
   * Get all items for a menu
   * @param {string} menuId - The menu ID
   * @param {boolean} includeDeleted - Whether to include deleted items
   * @returns {Promise<Object>} The items
   */
  async getMenuItems(menuId, includeDeleted = false) {
    this.debug(`Getting items for menu: ${menuId}`);
    try {
      const options = {
        filters: {
          menuId,
          ...(includeDeleted ? {} : { deleted: false }),
        },
        orderBy: "displayOrder",
      };
      return await this.getAll("*", options);
    } catch (error) {
      this.debug(`Error getting items for menu: %O`, error);
      throw error;
    }
  }

  /**
   * Get all items for a category
   * @param {string} categoryId - The category ID
   * @param {boolean} includeDeleted - Whether to include deleted items
   * @returns {Promise<Object>} The items
   */
  async getCategoryItems(categoryId, includeDeleted = false) {
    this.debug(`Getting items for category: ${categoryId}`);
    try {
      const options = {
        filters: {
          categoryId,
          ...(includeDeleted ? {} : { deleted: false }),
        },
        orderBy: "displayOrder",
      };
      return await this.getAll("*", options);
    } catch (error) {
      this.debug(`Error getting items for category: %O`, error);
      throw error;
    }
  }

  /**
   * Get an item by ID
   * @param {string} itemId - The item ID
   * @returns {Promise<Object>} The item
   */
  async getItem(itemId) {
    this.debug(`Getting item: ${itemId}`);
    try {
      return await this.getById(itemId);
    } catch (error) {
      this.debug(`Error getting item: %O`, error);
      throw error;
    }
  }

  /**
   * Create a new item
   * @param {Object} itemData - The item data
   * @returns {Promise<Object>} The created item
   */
  async createItem(itemData) {
    this.debug(`Creating item for menu: ${itemData.menuId}`);
    try {
      return await this.create(itemData);
    } catch (error) {
      this.debug(`Error creating item: %O`, error);
      throw error;
    }
  }

  /**
   * Update an item
   * @param {string} itemId - The item ID
   * @param {Object} updates - The updates to apply
   * @returns {Promise<Object>} The updated item
   */
  async updateItem(itemId, updates) {
    this.debug(`Updating item: ${itemId}`);
    try {
      return await this.update(updates, { id: itemId });
    } catch (error) {
      this.debug(`Error updating item: %O`, error);
      throw error;
    }
  }

  /**
   * Soft delete an item
   * @param {string} itemId - The item ID
   * @returns {Promise<Object>} The result of the operation
   */
  async deleteItem(itemId) {
    this.debug(`Soft deleting item: ${itemId}`);
    try {
      return await this.update({ deleted: true }, { id: itemId });
    } catch (error) {
      this.debug(`Error deleting item: %O`, error);
      throw error;
    }
  }
}
