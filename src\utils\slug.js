import { createDebugger } from "src/utils/debug";

const debug = createDebugger("utils:slug");

/**
 * Converts a string to kebab-case (lowercase with hyphens)
 * @param {string} str - The string to convert
 * @returns {string} - The kebab-case string
 */
export function toKebabCase(str) {
  return str
    .toLowerCase()
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/[^a-z0-9-]/g, "") // Remove non-alphanumeric characters except hyphens
    .replace(/-+/g, "-") // Replace multiple hyphens with a single hyphen
    .replace(/^-|-$/g, ""); // Remove leading and trailing hyphens
}

/**
 * Generates a list of slug suggestions based on business name and location
 * @param {Object} business - The business object with name, suburb, and city
 * @param {number} [count=5] - Number of suggestions to generate
 * @returns {Array<string>} - Array of slug suggestions
 */
export function generateSlugSuggestions(business, count = 5) {
  const { name, suburb, city } = business;
  debug("Generating slug suggestions for: %s in %s, %s", name, suburb, city);

  if (!name) {
    debug("No business name provided, cannot generate suggestions");
    return [];
  }

  const suggestions = [];

  // Base slug from business name
  const baseSlug = toKebabCase(name);
  suggestions.push(baseSlug);

  // Add suburb if available
  if (suburb) {
    suggestions.push(`${baseSlug}-${toKebabCase(suburb)}`);
  }

  // Add city if available
  if (city) {
    suggestions.push(`${baseSlug}-${toKebabCase(city)}`);
  }

  // Add suburb and city if both available
  if (suburb && city) {
    suggestions.push(`${baseSlug}-${toKebabCase(suburb)}-${toKebabCase(city)}`);
  }

  // Add numeric suffixes to the base slug
  for (let i = 1; suggestions.length < count; i++) {
    suggestions.push(`${baseSlug}-${i}`);
  }

  debug("Generated %d slug suggestions: %O", suggestions.length, suggestions);
  return suggestions;
}

/**
 * Checks if a slug is available and returns the first available slug from suggestions
 * @param {Function} checkAvailability - Function to check if a slug is available
 * @param {Array<string>} suggestions - Array of slug suggestions
 * @returns {Promise<Object>} - Object with the first available slug and all suggestions
 */
export async function findAvailableSlug(checkAvailability, suggestions) {
  debug("Checking availability for %d slug suggestions", suggestions.length);

  const results = [];
  let firstAvailable = null;

  for (const slug of suggestions) {
    try {
      const result = await checkAvailability(slug);
      results.push({
        slug,
        available: result.available,
      });

      if (result.available && !firstAvailable) {
        firstAvailable = slug;
      }
    } catch (error) {
      debug("Error checking availability for slug '%s': %O", slug, error);
      results.push({
        slug,
        available: false,
        error: true,
      });
    }
  }

  debug("Availability check results: %O", results);
  debug("First available slug: %s", firstAvailable);

  return {
    firstAvailable,
    results,
  };
}

/**
 * Generates a category slug from a name
 * @param {string} name - The category name
 * @returns {string} - The generated slug
 */
export function generateCategorySlug(name) {
  if (!name || typeof name !== "string") {
    return "";
  }

  return toKebabCase(name);
}

/**
 * Validates a category slug format
 * @param {string} slug - The slug to validate
 * @returns {Object} - Validation result with isValid flag and error message
 */
export function validateCategorySlug(slug) {
  if (!slug || typeof slug !== "string") {
    return {
      isValid: false,
      error: "Slug is required",
    };
  }

  // Check length
  if (slug.length < 2) {
    return {
      isValid: false,
      error: "Slug must be at least 2 characters long",
    };
  }

  if (slug.length > 50) {
    return {
      isValid: false,
      error: "Slug must be no more than 50 characters long",
    };
  }

  // Check format (lowercase letters, numbers, and hyphens only)
  const slugRegex = /^[a-z0-9-]+$/;
  if (!slugRegex.test(slug)) {
    return {
      isValid: false,
      error: "Slug can only contain lowercase letters, numbers, and hyphens",
    };
  }

  // Check that it doesn't start or end with hyphen
  if (slug.startsWith("-") || slug.endsWith("-")) {
    return {
      isValid: false,
      error: "Slug cannot start or end with a hyphen",
    };
  }

  // Check for consecutive hyphens
  if (slug.includes("--")) {
    return {
      isValid: false,
      error: "Slug cannot contain consecutive hyphens",
    };
  }

  return {
    isValid: true,
    error: null,
  };
}
