<template>
  <q-dialog
    ref="dialogRef"
    persistent
    :maximized="$q.screen.lt.sm"
    transition-show="scale"
    transition-hide="scale"
    @hide="onDialogHide"
  >
    <q-card class="dialog-card">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">{{ title }}</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-separator class="q-my-sm" />

      <q-card-section class="q-pt-none">
        <q-form @submit="onSubmit" class="q-gutter-md">
          <!-- Basic Information -->
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-8">
              <!-- Item Name -->
              <q-input
                v-model="form.name"
                :label="$t('business.menu.itemName')"
                :rules="[
                  (val) =>
                    !!val ||
                    $t('validation.required', {
                      field: $t('business.menu.itemName'),
                    }),
                ]"
                outlined
                dense
                autofocus
              />
            </div>
            <div class="col-12 col-md-4">
              <!-- Base Price -->
              <q-input
                v-model.number="form.basePrice"
                :label="$t('business.menu.basePrice')"
                type="number"
                step="0.25"
                min="0"
                max="1000"
                prefix="$"
                mask="#.##"
                fill-mask="0"
                reverse-fill-mask
                :rules="[
                  (val) => val >= 0 || $t('validation.minValue', { value: 0 }),
                ]"
                outlined
                dense
              >
                <template v-slot:prepend>
                  <q-icon name="paid" />
                </template>
              </q-input>
            </div>
          </div>

          <!-- Item Description -->
          <q-input
            v-model="form.description"
            :label="$t('business.menu.itemDescription')"
            type="textarea"
            outlined
            dense
            autogrow
          />

          <!-- Item Sizes -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-xs">
              {{ $t("business.menu.itemSizes") }}
            </div>
            <div class="text-caption q-mb-sm">
              {{ $t("business.menu.itemSizesHint") }}
            </div>

            <div v-if="form.sizes.length > 0" class="q-mb-sm">
              <q-list bordered separator>
                <q-item v-for="(size, index) in form.sizes" :key="index">
                  <q-item-section>
                    <q-item-label>{{ size.name }}</q-item-label>
                    <q-item-label caption>
                      {{ $t("business.menu.priceAdjustment") }}:
                      <span
                        :class="
                          size.priceAdjustment >= 0
                            ? 'text-positive'
                            : 'text-negative'
                        "
                      >
                        {{ size.priceAdjustment >= 0 ? "+" : "" }}${{
                          size.priceAdjustment.toFixed(2)
                        }}
                      </span>
                    </q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-btn
                      flat
                      round
                      dense
                      color="negative"
                      icon="delete"
                      @click="removeSize(index)"
                    />
                  </q-item-section>
                </q-item>
              </q-list>
            </div>

            <q-card flat bordered class="q-pa-sm">
              <div class="text-caption q-mb-sm">
                {{ $t("business.menu.addNewSize") }}
              </div>

              <div class="row q-col-gutter-sm">
                <div class="col-8">
                  <q-select
                    v-model="newSize.name"
                    :options="availableSizes"
                    :label="$t('business.menu.selectSize')"
                    outlined
                    dense
                    emit-value
                    map-options
                    class="q-mb-sm"
                  >
                    <template v-slot:prepend>
                      <q-icon name="straighten" />
                    </template>
                  </q-select>
                </div>
                <div class="col-4">
                  <q-input
                    v-model.number="newSize.priceAdjustment"
                    :label="$t('business.menu.priceAdjustment')"
                    type="number"
                    step="0.25"
                    min="-100"
                    max="100"
                    prefix="$"
                    mask="#.##"
                    fill-mask="0"
                    reverse-fill-mask
                    outlined
                    dense
                    class="q-mb-sm"
                  >
                    <template v-slot:prepend>
                      <q-icon name="paid" />
                    </template>
                  </q-input>
                </div>
              </div>

              <q-btn
                :label="$t('business.menu.addSize')"
                color="primary"
                icon="add"
                :disable="!newSize.name"
                @click="addSize"
              />
            </q-card>
          </div>

          <!-- Item Variations -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-xs">
              {{ $t("business.menu.itemVariations") }}
            </div>
            <div class="text-caption q-mb-sm">
              {{ $t("business.menu.itemVariationsHint") }}
            </div>

            <div v-if="form.variations.length > 0" class="q-mb-sm">
              <q-list bordered separator>
                <q-item
                  v-for="(variation, index) in form.variations"
                  :key="index"
                >
                  <q-item-section>
                    <q-item-label>{{ variation.name }}</q-item-label>
                    <q-item-label caption>
                      {{ $t("business.menu.priceAdjustment") }}:
                      <span
                        :class="
                          variation.priceAdjustment >= 0
                            ? 'text-positive'
                            : 'text-negative'
                        "
                      >
                        {{ variation.priceAdjustment >= 0 ? "+" : "" }}${{
                          variation.priceAdjustment.toFixed(2)
                        }}
                      </span>
                    </q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-btn
                      flat
                      round
                      dense
                      color="negative"
                      icon="delete"
                      @click="removeVariation(index)"
                    />
                  </q-item-section>
                </q-item>
              </q-list>
            </div>

            <q-card flat bordered class="q-pa-sm">
              <div class="text-caption q-mb-sm">
                {{ $t("business.menu.addNewVariation") }}
              </div>

              <div class="row q-col-gutter-sm">
                <div class="col-8">
                  <q-input
                    v-model="newVariation.name"
                    :label="$t('business.menu.variationName')"
                    outlined
                    dense
                    class="q-mb-sm"
                  >
                    <template v-slot:prepend>
                      <q-icon name="style" />
                    </template>
                  </q-input>
                </div>
                <div class="col-4">
                  <q-input
                    v-model.number="newVariation.priceAdjustment"
                    :label="$t('business.menu.priceAdjustment')"
                    type="number"
                    step="0.25"
                    min="-100"
                    max="100"
                    prefix="$"
                    mask="#.##"
                    fill-mask="0"
                    reverse-fill-mask
                    outlined
                    dense
                    class="q-mb-sm"
                  >
                    <template v-slot:prepend>
                      <q-icon name="paid" />
                    </template>
                  </q-input>
                </div>
              </div>

              <q-btn
                :label="$t('business.menu.addVariation')"
                color="primary"
                icon="add"
                :disable="!newVariation.name"
                @click="addVariation"
              />
            </q-card>
          </div>

          <!-- Item Ingredients -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-xs">
              {{ $t("business.menu.itemIngredients") }}
            </div>
            <div class="text-caption q-mb-sm">
              {{ $t("business.menu.itemIngredientsHint") }}
            </div>

            <div class="row q-col-gutter-sm">
              <div class="col-12">
                <div class="row q-col-gutter-sm">
                  <div class="col">
                    <q-select
                      v-model="selectedIngredients"
                      :options="availableIngredients"
                      option-value="id"
                      option-label="name"
                      :label="$t('business.menu.selectIngredients')"
                      outlined
                      dense
                      multiple
                      use-chips
                      use-input
                      input-debounce="0"
                      @filter="filterIngredients"
                      @update:model-value="updateItemIngredients"
                      class="q-mb-sm"
                    >
                      <template v-slot:option="scope">
                        <q-item v-bind="scope.itemProps">
                          <q-item-section>
                            <q-item-label>{{ scope.opt.name }}</q-item-label>
                            <q-item-label caption>{{
                              scope.opt.category
                            }}</q-item-label>
                          </q-item-section>
                        </q-item>
                      </template>

                      <template v-slot:no-option>
                        <q-item>
                          <q-item-section class="text-grey">
                            {{ $t("business.menu.noIngredientsFound") }}
                          </q-item-section>
                        </q-item>
                      </template>

                      <template v-slot:selected-item="scope">
                        <q-chip
                          removable
                          dense
                          @remove="scope.removeAtIndex(scope.index)"
                          :tabindex="scope.tabindex"
                          color="primary"
                          text-color="white"
                        >
                          {{ scope.opt.name }}
                        </q-chip>
                      </template>
                    </q-select>
                  </div>
                  <div class="col-auto self-center">
                    <q-btn
                      round
                      dense
                      color="primary"
                      icon="add"
                      @click="openNewIngredientDialog"
                      class="q-mb-sm"
                    >
                      <q-tooltip>{{
                        $t("business.menu.addNewIngredient")
                      }}</q-tooltip>
                    </q-btn>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Availability Settings -->
          <q-expansion-item
            :label="$t('business.menu.availabilitySettings')"
            icon="schedule"
            header-class="text-primary"
            expand-icon-class="text-primary"
          >
            <q-card>
              <q-card-section>
                <q-toggle
                  v-model="form.isAvailable"
                  :label="$t('business.menu.itemAvailable')"
                  color="positive"
                  class="q-mb-md"
                />

                <div class="row q-col-gutter-md" v-if="form.isAvailable">
                  <div class="col-6">
                    <q-input
                      v-model="form.availabilityStartTime"
                      :label="$t('business.hours.openTime')"
                      type="time"
                      outlined
                      dense
                    />
                  </div>
                  <div class="col-6">
                    <q-input
                      v-model="form.availabilityEndTime"
                      :label="$t('business.hours.closeTime')"
                      type="time"
                      outlined
                      dense
                    />
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </q-expansion-item>

          <!-- Action Buttons -->
          <div class="row justify-end q-mt-md">
            <q-btn
              :label="$t('common.cancel')"
              color="grey-7"
              v-close-popup
              flat
              class="q-mr-sm"
            />
            <q-btn
              :label="$t('common.save')"
              color="primary"
              type="submit"
              :loading="loading"
            />
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </q-dialog>

  <!-- Ingredients Dialog -->
  <ingredients-dialog
    v-model="showIngredientsDialog"
    :ingredient="editingIngredient"
    @submit="handleIngredientSubmit"
    @get-categories="provideCategories"
    ref="ingredientsDialogRef"
  />
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { useDialogPluginComponent, useQuasar } from "quasar";
import { createDebugger } from "src/utils/debug";
import { useBusinessStore } from "src/stores/business";
import {
  generateId,
  generateIngredientId,
  isIngredientId,
  generateSizeId,
  isSizeId,
} from "src/utils/idGenerator";
import IngredientsDialog from "src/components/business/dialogs/IngredientsDialog.vue";

const debug = createDebugger("component:menu-item-dialog");
const { t } = useI18n();
const businessStore = useBusinessStore();
const $q = useQuasar();

// Dialog plugin setup
const { dialogRef, onDialogHide, onDialogOK, onDialogCancel } =
  useDialogPluginComponent();

// Props
const props = defineProps({
  item: {
    type: Object,
    default: () => ({
      id: null,
      name: "",
      description: "",
      basePrice: 0,
      isAvailable: true,
      availabilityStartTime: "",
      availabilityEndTime: "",
      sizes: [],
      variations: [],
      ingredients: [],
      categoryId: null,
    }),
  },
  categoryId: {
    type: String,
    default: null,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

// State
const loading = ref(false);
const form = ref({
  id: props.item.id,
  name: props.item.name,
  description: props.item.description || "",
  basePrice: props.item.basePrice || 0,
  isAvailable:
    props.item.isAvailable !== undefined ? props.item.isAvailable : true,
  availabilityStartTime: props.item.availabilityStartTime || "",
  availabilityEndTime: props.item.availabilityEndTime || "",
  sizes: props.item.sizes ? [...props.item.sizes] : [],
  variations: props.item.variations ? [...props.item.variations] : [],
  ingredients: props.item.ingredients ? [...props.item.ingredients] : [],
  categoryId: props.item.categoryId || props.categoryId,
});

// New size, variation, and ingredient state
const newSize = ref({ name: "", priceAdjustment: 0 });
const newVariation = ref({ name: "", priceAdjustment: 0 });
const selectedIngredients = ref([]);

// Ingredients dialog state
const showIngredientsDialog = ref(false);
const editingIngredient = ref(null);
const ingredientsDialogRef = ref(null);

// Business ingredients
const businessIngredients = computed(() => {
  if (
    businessStore.currentBusiness &&
    businessStore.currentBusiness.ingredients
  ) {
    return businessStore.currentBusiness.ingredients;
  }
  // Fallback to some default ingredients if none are defined in the business
  return [
    { id: "i-def01", name: "Lettuce", category: "vegetable" },
    { id: "i-def02", name: "Tomato", category: "vegetable" },
    { id: "i-def03", name: "Onion", category: "vegetable" },
    { id: "i-def04", name: "Cheese", category: "dairy" },
    { id: "i-def05", name: "Chicken", category: "protein" },
    { id: "i-def06", name: "Beef", category: "protein" },
    { id: "i-def07", name: "Garlic", category: "spice" },
    { id: "i-def08", name: "Salt", category: "spice" },
    { id: "i-def09", name: "Pepper", category: "spice" },
  ];
});

// Business sizes
const businessSizes = computed(() => {
  if (
    businessStore.currentBusiness &&
    businessStore.currentBusiness.size_options
  ) {
    return businessStore.currentBusiness.size_options.map((size) => ({
      label: size.name,
      value: size.id,
      data: size,
    }));
  }
  // Fallback to some default sizes if none are defined in the business
  return [
    {
      label: "Small",
      value: generateSizeId(),
      data: {
        name: "Small",
        adjustmentType: "absolute",
        defaultAdjustment: -2.0,
      },
    },
    {
      label: "Medium",
      value: generateSizeId(),
      data: {
        name: "Medium",
        adjustmentType: "absolute",
        defaultAdjustment: 0.0,
      },
    },
    {
      label: "Large",
      value: generateSizeId(),
      data: {
        name: "Large",
        adjustmentType: "absolute",
        defaultAdjustment: 2.0,
      },
    },
  ];
});

// Available ingredients and sizes
const availableIngredients = computed(() => {
  return businessIngredients.value;
});

const availableSizes = computed(() => {
  // Filter out sizes that are already added to the item
  const usedSizeIds = form.value.sizes.map((size) => size.sizeId);
  return businessSizes.value.filter(
    (size) => !usedSizeIds.includes(size.value)
  );
});

// Filtered ingredients for search
const filteredIngredients = ref([]);

// Computed
const title = computed(() => {
  return props.isEdit
    ? t("business.menu.editItem")
    : t("business.menu.addItem");
});

// Methods
function filterIngredients(val, update) {
  if (val === "") {
    update(() => {
      filteredIngredients.value = availableIngredients.value;
    });
    return;
  }

  const needle = val.toLowerCase();
  update(() => {
    filteredIngredients.value = availableIngredients.value.filter(
      (v) =>
        v.name.toLowerCase().indexOf(needle) > -1 ||
        v.category.toLowerCase().indexOf(needle) > -1
    );
  });
}

// Update the item ingredients when the multi-select changes
function updateItemIngredients() {
  // Clear the current item ingredients
  form.value.ingredients = [];

  // Add all selected ingredients to the item ingredients
  if (selectedIngredients.value && selectedIngredients.value.length > 0) {
    selectedIngredients.value.forEach((ingredient) => {
      form.value.ingredients.push({
        id: ingredient.id,
        name: ingredient.name,
        category: ingredient.category,
      });
    });
  }
}

// Initialize selected ingredients from existing item ingredients
function initializeSelectedIngredients() {
  if (form.value.ingredients && form.value.ingredients.length > 0) {
    // Map existing item ingredients to selected ingredients
    selectedIngredients.value = form.value.ingredients.map((ing) => ({
      id: ing.id || generateIngredientId(),
      name: ing.name,
      category: ing.category,
    }));
  }
}

// Size methods
function addSize() {
  if (!newSize.value.name) return;

  // Find the selected size in the business sizes
  const selectedSize = businessSizes.value.find(
    (size) => size.value === newSize.value.name
  );

  form.value.sizes.push({
    sizeId: selectedSize ? selectedSize.value : generateSizeId(),
    name: selectedSize ? selectedSize.data.name : newSize.value.name,
    priceAdjustment: parseFloat(newSize.value.priceAdjustment) || 0,
  });

  // Reset form
  newSize.value = { name: "", priceAdjustment: 0 };
}

function removeSize(index) {
  form.value.sizes.splice(index, 1);
}

// Variation methods
function addVariation() {
  if (!newVariation.value.name) return;

  form.value.variations.push({
    id: generateId("v-", 5), // Use the base generator with 'v-' prefix for variations
    name: newVariation.value.name,
    priceAdjustment: parseFloat(newVariation.value.priceAdjustment) || 0,
  });

  // Reset form
  newVariation.value = { name: "", priceAdjustment: 0 };
}

function removeVariation(index) {
  form.value.variations.splice(index, 1);
}

// Open the ingredients dialog to add a new ingredient
function openNewIngredientDialog() {
  debug("Opening ingredients dialog");
  editingIngredient.value = null; // Reset editing state
  showIngredientsDialog.value = true;
}

// Provide categories to the ingredients dialog
function provideCategories() {
  debug("Providing categories to ingredients dialog");

  // Extract unique categories from business ingredients
  const categories = businessIngredients.value
    .map((ing) => ing.category)
    .filter((value, index, self) => self.indexOf(value) === index);

  debug("Categories to provide:", categories);

  // Set categories in the dialog
  if (ingredientsDialogRef.value) {
    ingredientsDialogRef.value.setCategories(categories);
  }
}

// Handle ingredient submission from the dialog
function handleIngredientSubmit(ingredient) {
  debug("Handling ingredient submission:", ingredient);

  // Ensure the ingredient has an ID
  if (!ingredient.id) {
    ingredient.id = generateIngredientId();
  }

  // Check if ingredient already exists
  const existingIngredient = businessIngredients.value.find(
    (ing) => ing.name.toLowerCase() === ingredient.name.toLowerCase()
  );

  if (!existingIngredient) {
    // Add to business ingredients using the store method
    try {
      businessStore.addIngredient(ingredient);

      // Select the new ingredient
      selectedIngredients.value.push(ingredient);
      updateItemIngredients();

      // Show success notification
      $q.notify({
        type: "positive",
        message: t("business.menu.ingredientAdded"),
        icon: "check_circle",
      });
    } catch (error) {
      debug("Error adding ingredient to business:", error);
      $q.notify({
        type: "negative",
        message: t("business.menu.ingredientAddError"),
        icon: "error",
      });
    }
  } else {
    // Update existing ingredient if category changed
    if (existingIngredient.category !== ingredient.category) {
      try {
        businessStore.updateIngredient(existingIngredient.id, {
          category: ingredient.category,
        });

        // Show update notification
        $q.notify({
          type: "positive",
          message: t("business.menu.ingredientUpdated"),
          icon: "check_circle",
        });
      } catch (error) {
        debug("Error updating ingredient:", error);
        $q.notify({
          type: "negative",
          message: t("business.menu.ingredientUpdateError"),
          icon: "error",
        });
      }
    } else {
      // Show warning notification
      $q.notify({
        type: "warning",
        message: t("business.menu.ingredientAlreadyExists"),
        icon: "warning",
      });
    }
  }
}

function onSubmit() {
  debug("Submitting form", form.value);
  loading.value = true;

  // Generate slug from name if not editing
  if (!props.isEdit) {
    form.value.slug = form.value.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-|-$/g, "");
  }

  // Prepare the data for submission
  const itemData = {
    ...form.value,
  };

  // Remove id field for new items (let database auto-generate)
  if (!props.isEdit) {
    delete itemData.id;
  }

  // Convert empty time strings to null for database compatibility
  if (
    !itemData.availabilityStartTime ||
    itemData.availabilityStartTime === ""
  ) {
    itemData.availabilityStartTime = null;
  }
  if (!itemData.availabilityEndTime || itemData.availabilityEndTime === "") {
    itemData.availabilityEndTime = null;
  }

  debug("Item data being sent:", itemData);

  // Return the form data to the parent component
  onDialogOK(itemData);

  // Reset loading state (in case dialog is not closed)
  setTimeout(() => {
    loading.value = false;
  }, 500);
}

// Lifecycle
onMounted(() => {
  debug("Dialog mounted", { props });

  // Initialize selected ingredients from existing item ingredients
  initializeSelectedIngredients();
});
</script>

<style lang="scss" scoped>
.dialog-card {
  min-width: 400px;
  max-width: 800px;
  width: 90vw;
}

@media (max-width: 599px) {
  .dialog-card {
    width: 100%;
  }
}
</style>
