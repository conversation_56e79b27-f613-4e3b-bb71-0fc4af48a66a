<template>
  <q-dialog
    ref="dialogRef"
    v-model="localModelValue"
    persistent
    @hide="onDialogHide"
  >
    <q-card style="width: 500px; max-width: 90vw">
      <q-card-section class="row items-center">
        <div class="text-h6">
          {{ isEditing ? "Edit Ingredient" : "Add Ingredient" }}
        </div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form @submit="onSubmit" ref="formRef">
          <!-- Name -->
          <q-input
            v-model="form.name"
            label="Name"
            :rules="[(val) => !!val || 'Name is required']"
            dense
            outlined
            class="q-mb-md"
          />

          <!-- Category -->
          <q-select
            v-model="form.category"
            label="Category"
            :options="categoryOptions"
            use-input
            input-debounce="0"
            dense
            outlined
            class="q-mb-md"
            @filter="filterCategories"
            @new-value="createCategory"
            :rules="[(val) => !!val || 'Category is required']"
            emit-value
            map-options
            option-value="value"
            option-label="label"
          >
            <template v-slot:no-option>
              <q-item>
                <q-item-section class="text-grey">No results</q-item-section>
              </q-item>
            </template>
          </q-select>
        </q-form>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn label="Cancel" color="negative" flat v-close-popup />
        <q-btn
          :label="isEditing ? 'Save Changes' : 'Add Ingredient'"
          color="primary"
          :loading="loading"
          @click="onSubmit"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { useDialogPluginComponent } from "quasar";
import { createDebugger } from "src/utils/debug";
import { generateIngredientId } from "src/utils/idGenerator";

const debug = createDebugger("component:ingredients-dialog");

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  ingredient: {
    type: Object,
    default: () => null,
  },
});

// Emits
const emit = defineEmits([
  ...useDialogPluginComponent.emits,
  "update:modelValue",
  "submit",
]);

// Dialog plugin
const { dialogRef, onDialogHide } = useDialogPluginComponent();

// Local state
const formRef = ref(null);
const loading = ref(false);
const localModelValue = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

// Form state
const defaultForm = {
  id: "",
  name: "",
  category: "", // Empty by default, will be set when user selects or creates a category
};

const form = ref({ ...defaultForm });

// Category options
const categoryOptions = ref([]);
const originalCategoryFilter = ref("");
const originalCategoriesList = ref([]);

// Methods to expose to parent
defineExpose({
  setCategories(categories) {
    debug("Setting categories in dialog:", categories);
    console.log("Setting categories in dialog:", categories); // Console log for easier debugging

    // Store the original list of string values
    originalCategoriesList.value = [...categories];
    debug("Original categories list stored:", originalCategoriesList.value);

    // Create a new array to avoid reference issues and convert to objects with value/label
    const finalCategories = [...new Set(categories)].map((category) => ({
      value: category,
      label: category.charAt(0).toUpperCase() + category.slice(1), // Capitalize first letter
    }));

    categoryOptions.value = finalCategories;

    debug("Final category options set:", categoryOptions.value);
    console.log("Final category options set:", categoryOptions.value); // Console log for easier debugging
  },

  // Get the current category value
  getCurrentCategory() {
    debug("Getting current category:", form.value.category);
    console.log("Current form state:", form.value); // Console log for easier debugging
    return form.value.category;
  },
});

// Filter categories
function filterCategories(val, update) {
  debug("Filtering categories with input:", val);

  if (val === "") {
    debug("Empty input, resetting to original list");
    update(() => {
      // Reset to original list when empty
      const categories = [...originalCategoriesList.value];
      debug("Original categories for reset:", categories);

      // Convert to objects with value/label
      const formattedCategories = categories.map((category) => ({
        value: category,
        label: category.charAt(0).toUpperCase() + category.slice(1), // Capitalize first letter
      }));

      categoryOptions.value = formattedCategories;
      debug("Reset category options to:", categoryOptions.value);
    });
    return;
  }

  update(() => {
    const needle = val.toLowerCase();
    debug("Filtering with lowercase value:", needle);
    originalCategoryFilter.value = val;

    // Filter existing options
    const filteredOptions = originalCategoriesList.value.filter(
      (v) => v.toLowerCase().indexOf(needle) > -1
    );
    debug("Filtered options based on input:", filteredOptions);

    // Convert to objects with value/label
    const formattedOptions = filteredOptions.map((category) => ({
      value: category,
      label: category.charAt(0).toUpperCase() + category.slice(1), // Capitalize first letter
    }));

    categoryOptions.value = formattedOptions;

    // If exact match not found, show option to create new category
    const exactMatch = originalCategoriesList.value.some(
      (v) => v.toLowerCase() === needle
    );
    debug("Exact match found:", exactMatch);

    if (!exactMatch && needle.length > 0) {
      // Add option to create new category with the current input
      categoryOptions.value.push({
        value: val,
        label: val.charAt(0).toUpperCase() + val.slice(1) + " (New)",
      });
      debug("Added new category option:", val);
    }

    debug("Final filtered category options:", categoryOptions.value);
    console.log("Final filtered category options:", categoryOptions.value); // Console log for easier debugging
  });
}

// Create new category
function createCategory(val) {
  if (val) {
    debug("Creating new category:", val);
    console.log("Creating new category:", val); // Console log for easier debugging

    // Add the new category to the options if not already there
    if (!originalCategoriesList.value.includes(val)) {
      originalCategoriesList.value.push(val);
      debug(
        "Added new category to original list:",
        originalCategoriesList.value
      );
    } else {
      debug("Category already exists in original list");
    }

    // Set the form value
    form.value.category = val;
    debug("Set form category value to:", form.value.category);
    console.log("Current form values:", form.value); // Console log for easier debugging

    // Update the category options list with the new value
    const exists = categoryOptions.value.some((opt) => opt.value === val);
    if (!exists) {
      categoryOptions.value.push({
        value: val,
        label: val.charAt(0).toUpperCase() + val.slice(1),
      });
      debug("Added new category to options list");
    }
  }
}

// Computed
const isEditing = computed(() => !!props.ingredient);

// Watch for changes in the ingredient prop
watch(
  () => props.ingredient,
  (newValue) => {
    debug("Ingredient prop changed:", newValue);
    console.log("Ingredient prop changed:", newValue); // Console log for easier debugging

    if (newValue) {
      form.value = { ...newValue };
      debug("Form updated with ingredient data:", form.value);
      console.log("Form updated with ingredient data:", form.value); // Console log for easier debugging
    } else {
      resetForm();
      debug("Form reset to defaults");
    }
  },
  { immediate: true }
);

// Methods
function resetForm() {
  debug("Resetting form to defaults");
  form.value = { ...defaultForm, id: generateIngredientId() };
  debug("Form reset to:", form.value);
  console.log("Form reset to:", form.value); // Console log for easier debugging
}

async function onSubmit() {
  debug("Submitting form");
  console.log("Submitting form with values:", form.value); // Console log for easier debugging

  try {
    const isValid = await formRef.value.validate();
    debug("Form validation result:", isValid);

    if (!isValid) {
      debug("Form validation failed");
      return;
    }

    loading.value = true;

    // Emit the form data
    debug("Emitting submit event with data:", form.value);
    console.log("Emitting submit event with data:", form.value); // Console log for easier debugging
    emit("submit", { ...form.value });

    // Close the dialog
    debug("Closing dialog");
    localModelValue.value = false;
  } catch (error) {
    debug("Form validation error:", error);
    console.error("Form validation error:", error); // Console log for easier debugging
  } finally {
    loading.value = false;
  }
}

// Watch for dialog visibility changes
watch(
  () => localModelValue.value,
  (newValue) => {
    debug(`Dialog visibility changed to: ${newValue}`);
    if (newValue) {
      debug("Dialog is now visible, emitting get-categories event");
      emit("get-categories");
    }
  }
);

// Initialize
onMounted(() => {
  debug("IngredientsDialog component mounted");
  console.log("IngredientsDialog component mounted"); // Console log for easier debugging

  if (!isEditing.value) {
    resetForm();
  }

  // Request categories from parent when mounted
  debug("Emitting get-categories event on mount");
  console.log("Emitting get-categories event on mount"); // Console log for easier debugging
  emit("get-categories");

  // Log the current state
  console.log("Current form state:", form.value);
  console.log("Current category options:", categoryOptions.value);
});
</script>

<style lang="scss" scoped>
// Add any component-specific styles here
</style>
